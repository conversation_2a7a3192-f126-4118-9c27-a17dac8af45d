
[2025-07-21 06:53:01 DECODE TP0] Capture cuda graph end. Time elapsed: 9.60 s. mem usage=2.74 GB. avail mem=6.44 GB.
[2025-07-21 06:53:01 DECODE TP0] Scheduler initialized
[2025-07-21 06:53:01 DECODE TP0] Scheduler disaggregation_mode: DisaggregationMode.NULL
[2025-07-21 06:53:01 DECODE TP0] Scheduler enable_overlap: True
[2025-07-21 06:53:01 DECODE TP0] Instance role: InstanceRole.DECODE
[2025-07-21 06:53:01 DECODE TP0] Scheduler running in overlap mode
[2025-07-21 06:53:01 DECODE TP1] Scheduler initialized
[2025-07-21 06:53:01 DECODE TP1] Scheduler disaggregation_mode: DisaggregationMode.NULL
[2025-07-21 06:53:01 DECODE TP1] Scheduler enable_overlap: True
[2025-07-21 06:53:01 DECODE TP1] Instance role: InstanceRole.DECODE
[2025-07-21 06:53:01 DECODE TP1] Scheduler running in overlap mode
[2025-07-21 06:53:01] ✅ Decode scheduler 0 is ready
[2025-07-21 06:53:01] ✅ Decode scheduler 1 is ready
[2025-07-21 06:53:01] Waiting for all Prefill schedulers to be ready...
[2025-07-21 06:53:01] ✅ Prefill scheduler 0 is ready
[2025-07-21 06:53:01] ✅ Prefill scheduler 1 is ready
[2025-07-21 06:53:01] Waiting for Detokenizer to be ready...
✅ [V0.4.8] Semi-PD IPC extension loaded successfully
🔧 [SAFE WRAPPER] Function called - starting initialization
🔧 [SAFE WRAPPER] About to call run_detokenizer_process
[2025-07-21 06:53:06] 🔧 [DETOKENIZER] Starting DetokenizerManager initialization...
[2025-07-21 06:53:06] 🔧 [DETOKENIZER] Starting ZMQ socket initialization...
[2025-07-21 06:53:06] 🔧 [DETOKENIZER] recv_from_scheduler socket bound to: ipc:///tmp/semipd_30000_632364_detokenizer
[2025-07-21 06:53:06] 🔧 [DETOKENIZER] send_to_tokenizer socket connected to: ipc:///tmp/semipd_30000_632364_tokenizer
[2025-07-21 06:53:06] 🔧 [DETOKENIZER] Starting tokenizer loading from: /home/<USER>/model/Qwen/Qwen2.5-32B-Instruct
[2025-07-21 06:53:06] 🔧 [DETOKENIZER] Tokenizer loading completed!
[2025-07-21 06:53:06] 🔧 [DETOKENIZER] DetokenizerManager initialization completed!
[2025-07-21 06:53:06] 🔧 [DETOKENIZER] Sending ready signal to parent process...
[2025-07-21 06:53:06] 🔧 [DETOKENIZER] Ready signal sent successfully!
[2025-07-21 06:53:06] 🔧 [DETOKENIZER] Starting event loop...
[2025-07-21 06:53:06] 🔧 [ENGINE] Received data from detokenizer: {'status': 'ready'}
[2025-07-21 06:53:06] ✅ Detokenizer is ready
[2025-07-21 06:53:06] [TOKENIZER] 🔗 Connected to Decode scheduler: ipc:///tmp/semipd_30000_632364_d_scheduler
[2025-07-21 06:53:06] [TOKENIZER] 🔗 Connected to Prefill scheduler: ipc:///tmp/semipd_30000_632364_p_scheduler
[2025-07-21 06:53:06] Loading chat template: chatml
[2025-07-21 06:53:06] INFO:     Started server process [632364]
[2025-07-21 06:53:06] INFO:     Waiting for application startup.
[2025-07-21 06:53:06] INFO:     Application startup complete.
[2025-07-21 06:53:06] INFO:     Uvicorn running on http://127.0.0.1:30000 (Press CTRL+C to quit)
[2025-07-21 06:53:07] INFO:     127.0.0.1:52540 - "GET /get_model_info HTTP/1.1" 200 OK
[2025-07-21 06:53:07 PREFILL TP0] New request 25611c5726874a0a851abce2034d8f29, #tokens: 6
[2025-07-21 06:53:07 PREFILL TP0] [SEMI_PD] 🔧 Received sampling_params: temperature=1.0, top_k=1, top_p=1.0
[2025-07-21 06:53:07 DECODE TP1] New request 25611c5726874a0a851abce2034d8f29, #tokens: 6
[2025-07-21 06:53:07 DECODE TP1] [SEMI_PD] 🔧 Received sampling_params: temperature=1.0, top_k=1, top_p=1.0
[2025-07-21 06:53:07 PREFILL TP0] [PREFILL] Send request to D worker: GetNextPrefillBatchInput(rids=['25611c5726874a0a851abce2034d8f29'])
[2025-07-21 06:53:07 DECODE TP0] New request 25611c5726874a0a851abce2034d8f29, #tokens: 6
[2025-07-21 06:53:07 DECODE TP0] [SEMI_PD] 🔧 Received sampling_params: temperature=1.0, top_k=1, top_p=1.0
[2025-07-21 06:53:07 PREFILL TP1] New request 25611c5726874a0a851abce2034d8f29, #tokens: 6
[2025-07-21 06:53:07 PREFILL TP1] [SEMI_PD] 🔧 Received sampling_params: temperature=1.0, top_k=1, top_p=1.0
[2025-07-21 06:53:07 DECODE TP1] [DECODE] 🔥 get_next_prefill_batch called with rids: ['25611c5726874a0a851abce2034d8f29']
[2025-07-21 06:53:07 DECODE TP0] [DECODE] 🔥 get_next_prefill_batch called with rids: ['25611c5726874a0a851abce2034d8f29']
[2025-07-21 06:53:07 DECODE TP1] [DECODE] 🔧 DEBUG: Request 25611c5726874a0a851abce2034d8f29 origin_input_ids = [785, 6722, 3283, 315, 9625, 374]
[2025-07-21 06:53:07 DECODE TP0] [DECODE] 🔧 DEBUG: Request 25611c5726874a0a851abce2034d8f29 origin_input_ids = [785, 6722, 3283, 315, 9625, 374]
[2025-07-21 06:53:07 DECODE TP1] [DECODE] 🔧 DEBUG: Request 25611c5726874a0a851abce2034d8f29 origin_input_ids length = 6
[2025-07-21 06:53:07 DECODE TP1] [DECODE] 🔧 DEBUG: Request 25611c5726874a0a851abce2034d8f29 last token = 374
[2025-07-21 06:53:07 DECODE TP0] [DECODE] 🔧 DEBUG: Request 25611c5726874a0a851abce2034d8f29 origin_input_ids length = 6
[2025-07-21 06:53:07 DECODE TP1] [DECODE] Calling get_new_batch_prefill with rids: ['25611c5726874a0a851abce2034d8f29']
[2025-07-21 06:53:07 DECODE TP0] [DECODE] 🔧 DEBUG: Request 25611c5726874a0a851abce2034d8f29 last token = 374
[2025-07-21 06:53:07 DECODE TP0] [DECODE] Calling get_new_batch_prefill with rids: ['25611c5726874a0a851abce2034d8f29']
[2025-07-21 06:53:07 DECODE TP1] [DECODE] Processing waiting queue, rids=['25611c5726874a0a851abce2034d8f29'], waiting_queue_size=1
[2025-07-21 06:53:07 DECODE TP0] [DECODE] Processing waiting queue, rids=['25611c5726874a0a851abce2034d8f29'], waiting_queue_size=1
[2025-07-21 06:53:07 DECODE TP0] Prefill batch. #new-seq: 1, #new-token: 6, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0
[2025-07-21 06:53:07 DECODE TP1] [DECODE] get_new_batch_prefill returned: True
[2025-07-21 06:53:07 DECODE TP1] [DECODE] Send response to P worker: GetNextPrefillBatchOutput(rids=['25611c5726874a0a851abce2034d8f29'], chunked_rid=None, req_pool_indices=[0], prefix_lens=[0], extend_input_lens=[6], finished_reasons=None)
[2025-07-21 06:53:07 DECODE TP1] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23568, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23568
self.tree_cache.evictable_size()=0

[2025-07-21 06:53:07 DECODE TP1] Cache flushed successfully!
[2025-07-21 06:53:07 DECODE TP1] Memory leak resolved after cache flush
[2025-07-21 06:53:08 DECODE TP0] [DECODE] get_new_batch_prefill returned: True
[2025-07-21 06:53:08 DECODE TP0] [DECODE] Send response to P worker: GetNextPrefillBatchOutput(rids=['25611c5726874a0a851abce2034d8f29'], chunked_rid=None, req_pool_indices=[0], prefix_lens=[0], extend_input_lens=[6], finished_reasons=None)
[2025-07-21 06:53:08 DECODE TP0] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23568, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23568
self.tree_cache.evictable_size()=0

[2025-07-21 06:53:08 DECODE TP0] Cache flushed successfully!
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] Recv response from D worker: GetNextPrefillBatchOutput(rids=['25611c5726874a0a851abce2034d8f29'], chunked_rid=None, req_pool_indices=[0], prefix_lens=[0], extend_input_lens=[6], finished_reasons=None)
[2025-07-21 06:53:08 DECODE TP0] Memory leak resolved after cache flush
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] Creating batch from response: ['25611c5726874a0a851abce2034d8f29']
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] Creating batch from response: ['25611c5726874a0a851abce2034d8f29']
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🔧 Request 25611c5726874a0a851abce2034d8f29 sampling params: temperature=1.0, top_k=1, top_p=1.0
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🔧 Request 25611c5726874a0a851abce2034d8f29 sampling params: temperature=1.0, top_k=1, top_p=1.0
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🔧 Batch sampling info: temperatures=tensor([[1.]], device='cuda:0'), is_all_greedy=True
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] Successfully created batch with 1 requests
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] get_next_batch_to_run returning batch with 1 requests
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🔥 Semi-PD Prefill: Generating logits (no pipeline parallel)
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] ❌ Failed to check embedding output: tuple index out of range
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🔧 Using model.embed_tokens
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🚨 EMBEDDING: checksum=-1336.000000, ptr=0x7f6970000000
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🚨 EMBEDDING STATS: mean=-0.000003, std=0.019165, min=-0.769531, max=0.730469
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🚨 ZERO RATIO: 0.000000 (5/389283840)
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🚨 TOKEN 15 EMBEDDING: [-0.01141357421875, -0.0035552978515625, -0.0032958984375, -0.00244140625, -0.00958251953125]
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🚨 TOKEN 16 EMBEDDING: [-0.0081787109375, -0.0047607421875, -0.00107574462890625, 0.000896453857421875, 0.002410888671875]
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🚨 TOKEN 108386 EMBEDDING: N/A
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🔧 Using lm_head
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🚨 LM_HEAD: checksum=14720.000000, mean=0.000038, std=0.017700
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - vocab_size=152064
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - hidden_size=5120
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - num_layers=64
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - model_type=qwen2
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model training mode = False
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model device = cuda:0
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model dtype = torch.bfloat16
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.temperatures = tensor([[1.]], device='cuda:0')
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ks = tensor([1], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ps = tensor([1.], device='cuda:0')
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.is_all_greedy = True
[2025-07-21 06:53:08 PREFILL TP0] [PREFILL] 🔧 DEBUG: Original is_all_greedy=True, temperature=1.0
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🔧 Batch sampling info: temperatures=tensor([[1.]], device='cuda:1'), is_all_greedy=True
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] Successfully created batch with 1 requests
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] get_next_batch_to_run returning batch with 1 requests
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🔥 Semi-PD Prefill: Generating logits (no pipeline parallel)
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] ❌ Failed to check embedding output: tuple index out of range
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🔧 Using model.embed_tokens
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🚨 EMBEDDING: checksum=3600.000000, ptr=0x7a7f04000000
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🚨 EMBEDDING STATS: mean=0.000009, std=0.015625, min=-0.196289, max=0.183594
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🚨 ZERO RATIO: 0.000000 (56/389283840)
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🚨 TOKEN 15 EMBEDDING: [-0.0091552734375, 0.0025177001953125, 0.009033203125, -0.0166015625, 0.01434326171875]
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🚨 TOKEN 16 EMBEDDING: [-0.00537109375, -0.0086669921875, 0.0106201171875, 0.01318359375, -0.0019378662109375]
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🚨 TOKEN 108386 EMBEDDING: N/A
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🔧 Using lm_head
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🚨 LM_HEAD: checksum=11520.000000, mean=0.000030, std=0.017456
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - vocab_size=152064
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - hidden_size=5120
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - num_layers=64
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - model_type=qwen2
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model training mode = False
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model device = cuda:1
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model dtype = torch.bfloat16
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.temperatures = tensor([[1.]], device='cuda:1')
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ks = tensor([1], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ps = tensor([1.], device='cuda:1')
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.is_all_greedy = True
[2025-07-21 06:53:08 PREFILL TP1] [PREFILL] 🔧 DEBUG: Original is_all_greedy=True, temperature=1.0
✅ [V0.4.8] Semi-PD IPC extension loaded successfully
✅ [V0.4.8] Semi-PD IPC extension loaded successfully
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔧 VL Model: Extracted text components from tuple result
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits shape = torch.Size([1, 152064])
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits dtype = torch.float32
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits device = cuda:0
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits stats: mean=-0.567303, std=2.791093, min=-17.750000, max=17.875000
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits anomalies: nan_count=0, inf_count=0
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔧 VL Model: Extracted text components from tuple result
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits shape = torch.Size([1, 152064])
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits dtype = torch.float32
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits device = cuda:1
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits stats: mean=-0.567303, std=2.791093, min=-17.750000, max=17.875000
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits anomalies: nan_count=0, inf_count=0
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: top-5 logits = [17.875, 16.5, 16.0, 15.9375, 15.8125]
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: top-5 indices = [12095, 1304, 30743, 510, 2130]
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: token 16 logit value = -0.48828125
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: token 108386 logit value = 0.44921875
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: Generated next_token_ids = tensor([12095], device='cuda:1')
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: next_token_ids type = <class 'torch.Tensor'>
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔥 Generated logits and token IDs: tensor([12095], device='cuda:1')
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔥 process_batch_result_prefill called with result.next_token_ids=tensor([12095], device='cuda:1')
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔥 result type: <class 'sglang.srt.managers.scheduler.GenerationBatchResult'>
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔥 result.logits_output: True
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔥 Processing req 25611c5726874a0a851abce2034d8f29, next_token_id=12095
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔥 Added token 12095 to req 25611c5726874a0a851abce2034d8f29, output_ids=[12095]
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔥 Request 25611c5726874a0a851abce2034d8f29 continuing (skipping cache operations)
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔥 next_token_logits shape: (1, 152064)
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔧 KV Cache Status BEFORE: avail=0, expected=0, diff=0
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔧 batch.out_cache_loc exists: tensor([0, 1, 2, 3, 4, 5], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔧 CRITICAL FIX: Freeing KV cache allocated by Prefill instance
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] ✅ CRITICAL FIX: Successfully freed KV cache in Prefill instance
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔥 Creating BatchProcessPrefillResultReq with next_token_ids=[12095]
[2025-07-21 06:53:09 PREFILL TP1] [PREFILL] 🔥 Send response to D worker
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: top-5 logits = [17.875, 16.5, 16.0, 15.9375, 15.8125]
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: top-5 indices = [12095, 1304, 30743, 510, 2130]
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: token 16 logit value = -0.48828125
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: token 108386 logit value = 0.44921875
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: Generated next_token_ids = tensor([12095], device='cuda:0')
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: next_token_ids type = <class 'torch.Tensor'>
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔥 Generated logits and token IDs: tensor([12095], device='cuda:0')
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔥 process_batch_result_prefill called with result.next_token_ids=tensor([12095], device='cuda:0')
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔥 result type: <class 'sglang.srt.managers.scheduler.GenerationBatchResult'>
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔥 result.logits_output: True
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔥 Processing req 25611c5726874a0a851abce2034d8f29, next_token_id=12095
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔥 Added token 12095 to req 25611c5726874a0a851abce2034d8f29, output_ids=[12095]
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔥 Request 25611c5726874a0a851abce2034d8f29 continuing (skipping cache operations)
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔥 next_token_logits shape: (1, 152064)
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔧 KV Cache Status BEFORE: avail=0, expected=0, diff=0
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔧 batch.out_cache_loc exists: tensor([0, 1, 2, 3, 4, 5], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔧 CRITICAL FIX: Freeing KV cache allocated by Prefill instance
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] ✅ CRITICAL FIX: Successfully freed KV cache in Prefill instance
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔥 Creating BatchProcessPrefillResultReq with next_token_ids=[12095]
[2025-07-21 06:53:09 PREFILL TP0] [PREFILL] 🔥 Send response to D worker
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔥 process_prefill_result started, next_token_ids=[12095]
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔥 process_prefill_result started, next_token_ids=[12095]
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔥 Got batch with 1 requests
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔥 Got batch with 1 requests
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔥 Setting batch.output_ids...
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔧 KV Cache Status BEFORE process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔥 Setting batch.output_ids...
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔥 Calling process_batch_result_prefill...
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔥 process_batch_result_prefill called for Semi-PD Decode instance
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🚨 CRITICAL: Checking weight sharing in Decode instance...
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔧 KV Cache Status BEFORE process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔥 Calling process_batch_result_prefill...
[2025-07-21 06:53:09 DECODE TP1] [DECODE] ❌ Could not find model
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔧 DEBUG: tp_worker type = <class 'sglang.srt.managers.tp_worker_overlap_thread.TpModelWorkerClient'>
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔥 process_batch_result_prefill called for Semi-PD Decode instance
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🚨 CRITICAL: Checking weight sharing in Decode instance...
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔧 DEBUG: tp_worker attributes = ['device', 'forward_batch_generation', 'forward_stream', 'forward_thread', 'forward_thread_func', 'forward_thread_func_', 'future_token_ids_ct', 'future_token_ids_limit', 'future_token_ids_map', 'get_attention_tp_cpu_group', 'get_attention_tp_group', 'get_ipc_info', 'get_kv_cache', 'get_memory_pool', 'get_pad_input_ids_func', 'get_tp_group', 'get_weights_by_name', 'get_worker_info', 'gpu_id', 'hicache_layer_transfer_counter', 'init_attention_backend', 'init_cuda_graphs', 'init_weights_update_group', 'input_queue', 'max_running_requests', 'output_queue', 'parent_process', 'register_hicache_layer_transfer_counter', 'resolve_last_batch_result', 'scheduler_stream', 'set_hicache_consumer', 'share_params_from_ipc', 'update_weights_from_disk', 'update_weights_from_distributed', 'update_weights_from_tensor', 'worker']
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔧 DEBUG: Found tp_worker.worker
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔧 DEBUG: Found tp_worker.worker.model_runner
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔧 DEBUG: Found model via tp_worker.worker.model_runner.model
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔧 CRITICAL FIX: Called free_group_begin() for KV Cache management
[2025-07-21 06:53:09 DECODE TP0] [DECODE] ❌ Could not find model
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔧 DEBUG: tp_worker type = <class 'sglang.srt.managers.tp_worker_overlap_thread.TpModelWorkerClient'>
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔧 DEBUG: tp_worker attributes = ['device', 'forward_batch_generation', 'forward_stream', 'forward_thread', 'forward_thread_func', 'forward_thread_func_', 'future_token_ids_ct', 'future_token_ids_limit', 'future_token_ids_map', 'get_attention_tp_cpu_group', 'get_attention_tp_group', 'get_ipc_info', 'get_kv_cache', 'get_memory_pool', 'get_pad_input_ids_func', 'get_tp_group', 'get_weights_by_name', 'get_worker_info', 'gpu_id', 'hicache_layer_transfer_counter', 'init_attention_backend', 'init_cuda_graphs', 'init_weights_update_group', 'input_queue', 'max_running_requests', 'output_queue', 'parent_process', 'register_hicache_layer_transfer_counter', 'resolve_last_batch_result', 'scheduler_stream', 'set_hicache_consumer', 'share_params_from_ipc', 'update_weights_from_disk', 'update_weights_from_distributed', 'update_weights_from_tensor', 'worker']
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔧 DEBUG: Found tp_worker.worker
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔧 DEBUG: Found tp_worker.worker.model_runner
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔧 DEBUG: Found model via tp_worker.worker.model_runner.model
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔧 CRITICAL FIX: Called free_group_begin() for KV Cache management
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔧 DEBUG: Decode batch.out_cache_loc exists: tensor([1, 2, 3, 4, 5, 6], device='cuda:1')
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔧 DEBUG: Decode batch.out_cache_loc exists: tensor([1, 2, 3, 4, 5, 6], device='cuda:0')
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔧 CRITICAL FIX: Called free_group_end() to complete KV Cache management
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔥 process_batch_result_prefill completed for Semi-PD
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔧 KV Cache Status AFTER process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:53:09 DECODE TP1] [DECODE] ⚠️ KV Cache unchanged - potential memory leak!
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔥 process_batch_result_prefill completed!
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔥 Filtering batch...
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔥 Merging batch to running_batch...
[2025-07-21 06:53:09 DECODE TP1] [DECODE] 🔥 process_prefill_result completed successfully!
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔧 CRITICAL FIX: Called free_group_end() to complete KV Cache management
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔥 process_batch_result_prefill completed for Semi-PD
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔧 KV Cache Status AFTER process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:53:09 DECODE TP0] [DECODE] ⚠️ KV Cache unchanged - potential memory leak!
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔥 process_batch_result_prefill completed!
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔥 Filtering batch...
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔥 Merging batch to running_batch...
[2025-07-21 06:53:09 DECODE TP0] [DECODE] 🔥 process_prefill_result completed successfully!
[2025-07-21 06:53:12] INFO:     127.0.0.1:52544 - "POST /generate HTTP/1.1" 200 OK
[2025-07-21 06:53:12] The server is fired up and ready to roll!
[2025-07-21 06:53:13 DECODE TP0] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23587, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23587
self.tree_cache.evictable_size()=0

[2025-07-21 06:53:13 DECODE TP1] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23587, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23587
self.tree_cache.evictable_size()=0

[2025-07-21 06:53:13 DECODE TP0] Cache flushed successfully!
[2025-07-21 06:53:13 DECODE TP1] Cache flushed successfully!
[2025-07-21 06:53:13 DECODE TP0] Memory leak resolved after cache flush
[2025-07-21 06:53:13 DECODE TP1] Memory leak resolved after cache flush
[2025-07-21 06:55:06 PREFILL TP0] New request b1d655c2f131491eb66c9a6a70533b52, #tokens: 21
[2025-07-21 06:55:06 PREFILL TP0] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:06 DECODE TP1] New request b1d655c2f131491eb66c9a6a70533b52, #tokens: 21
[2025-07-21 06:55:06 DECODE TP0] New request b1d655c2f131491eb66c9a6a70533b52, #tokens: 21
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] Send request to D worker: GetNextPrefillBatchInput(rids=['b1d655c2f131491eb66c9a6a70533b52'])
[2025-07-21 06:55:06 DECODE TP1] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:06 DECODE TP0] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔥 get_next_prefill_batch called with rids: ['b1d655c2f131491eb66c9a6a70533b52']
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔥 get_next_prefill_batch called with rids: ['b1d655c2f131491eb66c9a6a70533b52']
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔧 DEBUG: Request b1d655c2f131491eb66c9a6a70533b52 origin_input_ids = [151644, 8948, 198, 2610, 525, 264, 10950, 17847, 13, 151645, 198, 151644, 872, 198, 105043, 100165, 151645, 198, 151644, 77091, 198]
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔧 DEBUG: Request b1d655c2f131491eb66c9a6a70533b52 origin_input_ids length = 21
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔧 DEBUG: Request b1d655c2f131491eb66c9a6a70533b52 origin_input_ids = [151644, 8948, 198, 2610, 525, 264, 10950, 17847, 13, 151645, 198, 151644, 872, 198, 105043, 100165, 151645, 198, 151644, 77091, 198]
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔧 DEBUG: Request b1d655c2f131491eb66c9a6a70533b52 last token = 198
[2025-07-21 06:55:06 DECODE TP0] [DECODE] Calling get_new_batch_prefill with rids: ['b1d655c2f131491eb66c9a6a70533b52']
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔧 DEBUG: Request b1d655c2f131491eb66c9a6a70533b52 origin_input_ids length = 21
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔧 DEBUG: Request b1d655c2f131491eb66c9a6a70533b52 last token = 198
[2025-07-21 06:55:06 DECODE TP1] [DECODE] Calling get_new_batch_prefill with rids: ['b1d655c2f131491eb66c9a6a70533b52']
[2025-07-21 06:55:06 DECODE TP0] [DECODE] Processing waiting queue, rids=['b1d655c2f131491eb66c9a6a70533b52'], waiting_queue_size=1
[2025-07-21 06:55:06 DECODE TP1] [DECODE] Processing waiting queue, rids=['b1d655c2f131491eb66c9a6a70533b52'], waiting_queue_size=1
[2025-07-21 06:55:06 PREFILL TP1] New request b1d655c2f131491eb66c9a6a70533b52, #tokens: 21
[2025-07-21 06:55:06 DECODE TP0] Prefill batch. #new-seq: 1, #new-token: 21, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0
[2025-07-21 06:55:06 PREFILL TP1] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:06 DECODE TP0] [DECODE] get_new_batch_prefill returned: True
[2025-07-21 06:55:06 DECODE TP0] [DECODE] Send response to P worker: GetNextPrefillBatchOutput(rids=['b1d655c2f131491eb66c9a6a70533b52'], chunked_rid=None, req_pool_indices=[0], prefix_lens=[0], extend_input_lens=[21], finished_reasons=None)
[2025-07-21 06:55:06 DECODE TP1] [DECODE] get_new_batch_prefill returned: True
[2025-07-21 06:55:06 DECODE TP0] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23553, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23553
self.tree_cache.evictable_size()=0

[2025-07-21 06:55:06 DECODE TP1] [DECODE] Send response to P worker: GetNextPrefillBatchOutput(rids=['b1d655c2f131491eb66c9a6a70533b52'], chunked_rid=None, req_pool_indices=[0], prefix_lens=[0], extend_input_lens=[21], finished_reasons=None)
[2025-07-21 06:55:06 DECODE TP1] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23553, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23553
self.tree_cache.evictable_size()=0

[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] Recv response from D worker: GetNextPrefillBatchOutput(rids=['b1d655c2f131491eb66c9a6a70533b52'], chunked_rid=None, req_pool_indices=[0], prefix_lens=[0], extend_input_lens=[21], finished_reasons=None)
[2025-07-21 06:55:06 DECODE TP0] Cache flushed successfully!
[2025-07-21 06:55:06 DECODE TP0] Memory leak resolved after cache flush
[2025-07-21 06:55:06 DECODE TP1] Cache flushed successfully!
[2025-07-21 06:55:06 DECODE TP1] Memory leak resolved after cache flush
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] Creating batch from response: ['b1d655c2f131491eb66c9a6a70533b52']
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] Creating batch from response: ['b1d655c2f131491eb66c9a6a70533b52']
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 Request b1d655c2f131491eb66c9a6a70533b52 sampling params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 Request b1d655c2f131491eb66c9a6a70533b52 sampling params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 Batch sampling info: temperatures=tensor([[0.7000]], device='cuda:0'), is_all_greedy=False
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] Successfully created batch with 1 requests
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] get_next_batch_to_run returning batch with 1 requests
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔥 Semi-PD Prefill: Generating logits (no pipeline parallel)
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] ❌ Failed to check embedding output: tuple index out of range
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 Using model.embed_tokens
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 Batch sampling info: temperatures=tensor([[0.7000]], device='cuda:1'), is_all_greedy=False
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] Successfully created batch with 1 requests
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] get_next_batch_to_run returning batch with 1 requests
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔥 Semi-PD Prefill: Generating logits (no pipeline parallel)
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] ❌ Failed to check embedding output: tuple index out of range
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 Using model.embed_tokens
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🚨 EMBEDDING: checksum=-1336.000000, ptr=0x7f6970000000
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🚨 EMBEDDING STATS: mean=-0.000003, std=0.019165, min=-0.769531, max=0.730469
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🚨 EMBEDDING: checksum=3600.000000, ptr=0x7a7f04000000
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🚨 EMBEDDING STATS: mean=0.000009, std=0.015625, min=-0.196289, max=0.183594
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🚨 ZERO RATIO: 0.000000 (5/389283840)
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🚨 TOKEN 15 EMBEDDING: [-0.01141357421875, -0.0035552978515625, -0.0032958984375, -0.00244140625, -0.00958251953125]
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🚨 TOKEN 16 EMBEDDING: [-0.0081787109375, -0.0047607421875, -0.00107574462890625, 0.000896453857421875, 0.002410888671875]
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🚨 TOKEN 108386 EMBEDDING: N/A
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 Using lm_head
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🚨 ZERO RATIO: 0.000000 (56/389283840)
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🚨 TOKEN 15 EMBEDDING: [-0.0091552734375, 0.0025177001953125, 0.009033203125, -0.0166015625, 0.01434326171875]
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🚨 TOKEN 16 EMBEDDING: [-0.00537109375, -0.0086669921875, 0.0106201171875, 0.01318359375, -0.0019378662109375]
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🚨 TOKEN 108386 EMBEDDING: N/A
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 Using lm_head
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🚨 LM_HEAD: checksum=14720.000000, mean=0.000038, std=0.017700
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - vocab_size=152064
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - hidden_size=5120
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - num_layers=64
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - model_type=qwen2
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model training mode = False
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model device = cuda:0
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model dtype = torch.bfloat16
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.temperatures = tensor([[0.7000]], device='cuda:0')
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ks = tensor([1073741824], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ps = tensor([1.], device='cuda:0')
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.is_all_greedy = False
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: Original is_all_greedy=False, temperature=0.699999988079071
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🚨 LM_HEAD: checksum=11520.000000, mean=0.000030, std=0.017456
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - vocab_size=152064
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - hidden_size=5120
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - num_layers=64
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - model_type=qwen2
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model training mode = False
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model device = cuda:1
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model dtype = torch.bfloat16
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.temperatures = tensor([[0.7000]], device='cuda:1')
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ks = tensor([1073741824], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ps = tensor([1.], device='cuda:1')
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.is_all_greedy = False
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: Original is_all_greedy=False, temperature=0.699999988079071
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 VL Model: Extracted text components from tuple result
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits shape = torch.Size([1, 152064])
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits dtype = torch.float32
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits device = cuda:0
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits stats: mean=0.000007, std=0.002564, min=0.000000, max=0.999989
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits anomalies: nan_count=0, inf_count=0
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: top-5 logits = [0.9999891519546509, 1.0879998626478482e-05, 1.4421160932087673e-09, 1.4151962934860762e-10, 8.28244625661334e-11]
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: top-5 indices = [104198, 35946, 111308, 108386, 128296]
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: token 16 logit value = 9.311456350223675e-19
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: token 108386 logit value = 1.4151962934860762e-10
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: Generated next_token_ids = tensor([104198], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 DEBUG: next_token_ids type = <class 'torch.Tensor'>
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔥 Generated logits and token IDs: tensor([104198], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔥 process_batch_result_prefill called with result.next_token_ids=tensor([104198], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔥 result type: <class 'sglang.srt.managers.scheduler.GenerationBatchResult'>
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔥 result.logits_output: True
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔥 Processing req b1d655c2f131491eb66c9a6a70533b52, next_token_id=104198
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔥 Added token 104198 to req b1d655c2f131491eb66c9a6a70533b52, output_ids=[104198]
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔥 Request b1d655c2f131491eb66c9a6a70533b52 continuing (skipping cache operations)
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔥 next_token_logits shape: (1, 152064)
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 KV Cache Status BEFORE: avail=6, expected=0, diff=6
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 batch.out_cache_loc exists: tensor([ 0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17,
        18, 19, 20], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔧 CRITICAL FIX: Freeing KV cache allocated by Prefill instance
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] ✅ CRITICAL FIX: Successfully freed KV cache in Prefill instance
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔥 Creating BatchProcessPrefillResultReq with next_token_ids=[104198]
[2025-07-21 06:55:06 PREFILL TP0] [PREFILL] 🔥 Send response to D worker
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 VL Model: Extracted text components from tuple result
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits shape = torch.Size([1, 152064])
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits dtype = torch.float32
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits device = cuda:1
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits stats: mean=0.000007, std=0.002564, min=0.000000, max=0.999989
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits anomalies: nan_count=0, inf_count=0
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: top-5 logits = [0.9999891519546509, 1.0879998626478482e-05, 1.4421160932087673e-09, 1.4151962934860762e-10, 8.28244625661334e-11]
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: top-5 indices = [104198, 35946, 111308, 108386, 128296]
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: token 16 logit value = 9.311456350223675e-19
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: token 108386 logit value = 1.4151962934860762e-10
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: Generated next_token_ids = tensor([104198], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 DEBUG: next_token_ids type = <class 'torch.Tensor'>
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔥 Generated logits and token IDs: tensor([104198], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔥 process_batch_result_prefill called with result.next_token_ids=tensor([104198], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔥 result type: <class 'sglang.srt.managers.scheduler.GenerationBatchResult'>
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔥 result.logits_output: True
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔥 Processing req b1d655c2f131491eb66c9a6a70533b52, next_token_id=104198
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔥 Added token 104198 to req b1d655c2f131491eb66c9a6a70533b52, output_ids=[104198]
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔥 Request b1d655c2f131491eb66c9a6a70533b52 continuing (skipping cache operations)
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔥 next_token_logits shape: (1, 152064)
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 KV Cache Status BEFORE: avail=6, expected=0, diff=6
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 batch.out_cache_loc exists: tensor([ 0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17,
        18, 19, 20], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔧 CRITICAL FIX: Freeing KV cache allocated by Prefill instance
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] ✅ CRITICAL FIX: Successfully freed KV cache in Prefill instance
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔥 Creating BatchProcessPrefillResultReq with next_token_ids=[104198]
[2025-07-21 06:55:06 PREFILL TP1] [PREFILL] 🔥 Send response to D worker
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔥 process_prefill_result started, next_token_ids=[104198]
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔥 Got batch with 1 requests
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔥 process_prefill_result started, next_token_ids=[104198]
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔥 Got batch with 1 requests
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔥 Setting batch.output_ids...
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔥 Setting batch.output_ids...
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔧 KV Cache Status BEFORE process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔧 KV Cache Status BEFORE process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔥 Calling process_batch_result_prefill...
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔥 Calling process_batch_result_prefill...
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔥 process_batch_result_prefill called for Semi-PD Decode instance
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🚨 CRITICAL: Checking weight sharing in Decode instance...
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔥 process_batch_result_prefill called for Semi-PD Decode instance
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🚨 CRITICAL: Checking weight sharing in Decode instance...
[2025-07-21 06:55:06 DECODE TP0] [DECODE] ❌ Could not find model
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔧 DEBUG: tp_worker type = <class 'sglang.srt.managers.tp_worker_overlap_thread.TpModelWorkerClient'>
[2025-07-21 06:55:06 DECODE TP1] [DECODE] ❌ Could not find model
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔧 DEBUG: tp_worker type = <class 'sglang.srt.managers.tp_worker_overlap_thread.TpModelWorkerClient'>
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔧 DEBUG: tp_worker attributes = ['cur_sampling_info', 'device', 'forward_batch_generation', 'forward_stream', 'forward_thread', 'forward_thread_func', 'forward_thread_func_', 'future_token_ids_ct', 'future_token_ids_limit', 'future_token_ids_map', 'get_attention_tp_cpu_group', 'get_attention_tp_group', 'get_ipc_info', 'get_kv_cache', 'get_memory_pool', 'get_pad_input_ids_func', 'get_tp_group', 'get_weights_by_name', 'get_worker_info', 'gpu_id', 'hicache_layer_transfer_counter', 'init_attention_backend', 'init_cuda_graphs', 'init_weights_update_group', 'input_queue', 'max_running_requests', 'output_queue', 'parent_process', 'register_hicache_layer_transfer_counter', 'resolve_last_batch_result', 'scheduler_stream', 'set_hicache_consumer', 'share_params_from_ipc', 'update_weights_from_disk', 'update_weights_from_distributed', 'update_weights_from_tensor', 'worker']
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔧 DEBUG: Found tp_worker.worker
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔧 DEBUG: Found tp_worker.worker.model_runner
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔧 DEBUG: tp_worker attributes = ['cur_sampling_info', 'device', 'forward_batch_generation', 'forward_stream', 'forward_thread', 'forward_thread_func', 'forward_thread_func_', 'future_token_ids_ct', 'future_token_ids_limit', 'future_token_ids_map', 'get_attention_tp_cpu_group', 'get_attention_tp_group', 'get_ipc_info', 'get_kv_cache', 'get_memory_pool', 'get_pad_input_ids_func', 'get_tp_group', 'get_weights_by_name', 'get_worker_info', 'gpu_id', 'hicache_layer_transfer_counter', 'init_attention_backend', 'init_cuda_graphs', 'init_weights_update_group', 'input_queue', 'max_running_requests', 'output_queue', 'parent_process', 'register_hicache_layer_transfer_counter', 'resolve_last_batch_result', 'scheduler_stream', 'set_hicache_consumer', 'share_params_from_ipc', 'update_weights_from_disk', 'update_weights_from_distributed', 'update_weights_from_tensor', 'worker']
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔧 DEBUG: Found model via tp_worker.worker.model_runner.model
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔧 DEBUG: Found tp_worker.worker
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔧 CRITICAL FIX: Called free_group_begin() for KV Cache management
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔧 DEBUG: Found tp_worker.worker.model_runner
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔧 DEBUG: Found model via tp_worker.worker.model_runner.model
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔧 CRITICAL FIX: Called free_group_begin() for KV Cache management
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔧 DEBUG: Decode batch.out_cache_loc exists: tensor([ 1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
        19, 20, 21], device='cuda:0')
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔧 DEBUG: Decode batch.out_cache_loc exists: tensor([ 1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
        19, 20, 21], device='cuda:1')
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔧 CRITICAL FIX: Called free_group_end() to complete KV Cache management
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔥 process_batch_result_prefill completed for Semi-PD
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔧 KV Cache Status AFTER process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:55:06 DECODE TP0] [DECODE] ⚠️ KV Cache unchanged - potential memory leak!
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔥 process_batch_result_prefill completed!
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔥 Filtering batch...
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔥 Merging batch to running_batch...
[2025-07-21 06:55:06 DECODE TP0] [DECODE] 🔥 process_prefill_result completed successfully!
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔧 CRITICAL FIX: Called free_group_end() to complete KV Cache management
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔥 process_batch_result_prefill completed for Semi-PD
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔧 KV Cache Status AFTER process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:55:06 DECODE TP1] [DECODE] ⚠️ KV Cache unchanged - potential memory leak!
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔥 process_batch_result_prefill completed!
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔥 Filtering batch...
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔥 Merging batch to running_batch...
[2025-07-21 06:55:06 DECODE TP1] [DECODE] 🔥 process_prefill_result completed successfully!
[2025-07-21 06:55:06] INFO:     127.0.0.1:54012 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-21 06:55:06 DECODE TP0] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23600, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23600
self.tree_cache.evictable_size()=0

[2025-07-21 06:55:06 DECODE TP1] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23600, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23600
self.tree_cache.evictable_size()=0

[2025-07-21 06:55:06 DECODE TP0] Cache flushed successfully!
[2025-07-21 06:55:06 DECODE TP1] Cache flushed successfully!
[2025-07-21 06:55:06 DECODE TP0] Memory leak resolved after cache flush
[2025-07-21 06:55:06 DECODE TP1] Memory leak resolved after cache flush
[2025-07-21 06:55:09 DECODE TP0] New request da84770cb85641388e28d3d3f502efd5, #tokens: 21
[2025-07-21 06:55:09 DECODE TP0] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:09 PREFILL TP0] New request da84770cb85641388e28d3d3f502efd5, #tokens: 21
[2025-07-21 06:55:09 DECODE TP1] New request da84770cb85641388e28d3d3f502efd5, #tokens: 21
[2025-07-21 06:55:09 PREFILL TP1] New request da84770cb85641388e28d3d3f502efd5, #tokens: 21
[2025-07-21 06:55:09 PREFILL TP0] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:09 DECODE TP1] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:09 PREFILL TP1] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] Send request to D worker: GetNextPrefillBatchInput(rids=['da84770cb85641388e28d3d3f502efd5'])
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔥 get_next_prefill_batch called with rids: ['da84770cb85641388e28d3d3f502efd5']
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔥 get_next_prefill_batch called with rids: ['da84770cb85641388e28d3d3f502efd5']
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔧 DEBUG: Request da84770cb85641388e28d3d3f502efd5 origin_input_ids = [151644, 8948, 198, 2610, 525, 264, 10950, 17847, 13, 151645, 198, 151644, 872, 198, 105043, 100165, 151645, 198, 151644, 77091, 198]
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔧 DEBUG: Request da84770cb85641388e28d3d3f502efd5 origin_input_ids length = 21
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔧 DEBUG: Request da84770cb85641388e28d3d3f502efd5 origin_input_ids = [151644, 8948, 198, 2610, 525, 264, 10950, 17847, 13, 151645, 198, 151644, 872, 198, 105043, 100165, 151645, 198, 151644, 77091, 198]
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔧 DEBUG: Request da84770cb85641388e28d3d3f502efd5 last token = 198
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔧 DEBUG: Request da84770cb85641388e28d3d3f502efd5 origin_input_ids length = 21
[2025-07-21 06:55:09 DECODE TP0] [DECODE] Calling get_new_batch_prefill with rids: ['da84770cb85641388e28d3d3f502efd5']
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔧 DEBUG: Request da84770cb85641388e28d3d3f502efd5 last token = 198
[2025-07-21 06:55:09 DECODE TP0] [DECODE] Processing waiting queue, rids=['da84770cb85641388e28d3d3f502efd5'], waiting_queue_size=1
[2025-07-21 06:55:09 DECODE TP1] [DECODE] Calling get_new_batch_prefill with rids: ['da84770cb85641388e28d3d3f502efd5']
[2025-07-21 06:55:09 DECODE TP1] [DECODE] Processing waiting queue, rids=['da84770cb85641388e28d3d3f502efd5'], waiting_queue_size=1
[2025-07-21 06:55:09 DECODE TP0] Prefill batch. #new-seq: 1, #new-token: 21, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0
[2025-07-21 06:55:09 DECODE TP0] [DECODE] get_new_batch_prefill returned: True
[2025-07-21 06:55:09 DECODE TP0] [DECODE] Send response to P worker: GetNextPrefillBatchOutput(rids=['da84770cb85641388e28d3d3f502efd5'], chunked_rid=None, req_pool_indices=[0], prefix_lens=[0], extend_input_lens=[21], finished_reasons=None)
[2025-07-21 06:55:09 DECODE TP1] [DECODE] get_new_batch_prefill returned: True
[2025-07-21 06:55:09 DECODE TP0] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23553, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23553
self.tree_cache.evictable_size()=0

[2025-07-21 06:55:09 DECODE TP1] [DECODE] Send response to P worker: GetNextPrefillBatchOutput(rids=['da84770cb85641388e28d3d3f502efd5'], chunked_rid=None, req_pool_indices=[0], prefix_lens=[0], extend_input_lens=[21], finished_reasons=None)
[2025-07-21 06:55:09 DECODE TP1] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23553, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23553
self.tree_cache.evictable_size()=0

[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] Recv response from D worker: GetNextPrefillBatchOutput(rids=['da84770cb85641388e28d3d3f502efd5'], chunked_rid=None, req_pool_indices=[0], prefix_lens=[0], extend_input_lens=[21], finished_reasons=None)
[2025-07-21 06:55:09 DECODE TP0] Cache flushed successfully!
[2025-07-21 06:55:09 DECODE TP0] Memory leak resolved after cache flush
[2025-07-21 06:55:09 DECODE TP1] Cache flushed successfully!
[2025-07-21 06:55:09 DECODE TP1] Memory leak resolved after cache flush
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] Creating batch from response: ['da84770cb85641388e28d3d3f502efd5']
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] Creating batch from response: ['da84770cb85641388e28d3d3f502efd5']
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 Request da84770cb85641388e28d3d3f502efd5 sampling params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 Request da84770cb85641388e28d3d3f502efd5 sampling params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 Batch sampling info: temperatures=tensor([[0.7000]], device='cuda:0'), is_all_greedy=False
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 Batch sampling info: temperatures=tensor([[0.7000]], device='cuda:1'), is_all_greedy=False
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] Successfully created batch with 1 requests
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] Successfully created batch with 1 requests
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] get_next_batch_to_run returning batch with 1 requests
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] get_next_batch_to_run returning batch with 1 requests
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔥 Semi-PD Prefill: Generating logits (no pipeline parallel)
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔥 Semi-PD Prefill: Generating logits (no pipeline parallel)
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] ❌ Failed to check embedding output: tuple index out of range
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] ❌ Failed to check embedding output: tuple index out of range
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 Using model.embed_tokens
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 Using model.embed_tokens
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🚨 EMBEDDING: checksum=-1336.000000, ptr=0x7f6970000000
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🚨 EMBEDDING: checksum=3600.000000, ptr=0x7a7f04000000
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🚨 EMBEDDING STATS: mean=-0.000003, std=0.019165, min=-0.769531, max=0.730469
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🚨 EMBEDDING STATS: mean=0.000009, std=0.015625, min=-0.196289, max=0.183594
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🚨 ZERO RATIO: 0.000000 (56/389283840)
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🚨 ZERO RATIO: 0.000000 (5/389283840)
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🚨 TOKEN 15 EMBEDDING: [-0.0091552734375, 0.0025177001953125, 0.009033203125, -0.0166015625, 0.01434326171875]
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🚨 TOKEN 15 EMBEDDING: [-0.01141357421875, -0.0035552978515625, -0.0032958984375, -0.00244140625, -0.00958251953125]
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🚨 TOKEN 16 EMBEDDING: [-0.00537109375, -0.0086669921875, 0.0106201171875, 0.01318359375, -0.0019378662109375]
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🚨 TOKEN 108386 EMBEDDING: N/A
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🚨 TOKEN 16 EMBEDDING: [-0.0081787109375, -0.0047607421875, -0.00107574462890625, 0.000896453857421875, 0.002410888671875]
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🚨 TOKEN 108386 EMBEDDING: N/A
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 Using lm_head
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 Using lm_head
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🚨 LM_HEAD: checksum=11520.000000, mean=0.000030, std=0.017456
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - vocab_size=152064
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🚨 LM_HEAD: checksum=14720.000000, mean=0.000038, std=0.017700
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - hidden_size=5120
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - num_layers=64
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - vocab_size=152064
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - model_type=qwen2
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - hidden_size=5120
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model training mode = False
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - num_layers=64
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - model_type=qwen2
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model device = cuda:1
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model training mode = False
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model dtype = torch.bfloat16
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model device = cuda:0
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model dtype = torch.bfloat16
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.temperatures = tensor([[0.7000]], device='cuda:1')
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.temperatures = tensor([[0.7000]], device='cuda:0')
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ks = tensor([1073741824], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ks = tensor([1073741824], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ps = tensor([1.], device='cuda:1')
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.is_all_greedy = False
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: Original is_all_greedy=False, temperature=0.699999988079071
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ps = tensor([1.], device='cuda:0')
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.is_all_greedy = False
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: Original is_all_greedy=False, temperature=0.699999988079071
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 VL Model: Extracted text components from tuple result
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 VL Model: Extracted text components from tuple result
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits shape = torch.Size([1, 152064])
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits shape = torch.Size([1, 152064])
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits dtype = torch.float32
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits dtype = torch.float32
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits device = cuda:0
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits device = cuda:1
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits stats: mean=0.000007, std=0.002564, min=0.000000, max=0.999989
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits stats: mean=0.000007, std=0.002564, min=0.000000, max=0.999989
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits anomalies: nan_count=0, inf_count=0
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits anomalies: nan_count=0, inf_count=0
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: top-5 logits = [0.9999891519546509, 1.0879998626478482e-05, 1.4421160932087673e-09, 1.4151962934860762e-10, 8.28244625661334e-11]
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: top-5 indices = [104198, 35946, 111308, 108386, 128296]
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: top-5 logits = [0.9999891519546509, 1.0879998626478482e-05, 1.4421160932087673e-09, 1.4151962934860762e-10, 8.28244625661334e-11]
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: token 16 logit value = 9.311456350223675e-19
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: top-5 indices = [104198, 35946, 111308, 108386, 128296]
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: token 108386 logit value = 1.4151962934860762e-10
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: token 16 logit value = 9.311456350223675e-19
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: token 108386 logit value = 1.4151962934860762e-10
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: Generated next_token_ids = tensor([104198], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 DEBUG: next_token_ids type = <class 'torch.Tensor'>
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: Generated next_token_ids = tensor([104198], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 DEBUG: next_token_ids type = <class 'torch.Tensor'>
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔥 Generated logits and token IDs: tensor([104198], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔥 Generated logits and token IDs: tensor([104198], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔥 process_batch_result_prefill called with result.next_token_ids=tensor([104198], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔥 result type: <class 'sglang.srt.managers.scheduler.GenerationBatchResult'>
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔥 result.logits_output: True
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔥 Processing req da84770cb85641388e28d3d3f502efd5, next_token_id=104198
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔥 Added token 104198 to req da84770cb85641388e28d3d3f502efd5, output_ids=[104198]
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔥 Request da84770cb85641388e28d3d3f502efd5 continuing (skipping cache operations)
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔥 process_batch_result_prefill called with result.next_token_ids=tensor([104198], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔥 result type: <class 'sglang.srt.managers.scheduler.GenerationBatchResult'>
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔥 result.logits_output: True
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔥 Processing req da84770cb85641388e28d3d3f502efd5, next_token_id=104198
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔥 Added token 104198 to req da84770cb85641388e28d3d3f502efd5, output_ids=[104198]
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔥 next_token_logits shape: (1, 152064)
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔥 Request da84770cb85641388e28d3d3f502efd5 continuing (skipping cache operations)
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 KV Cache Status BEFORE: avail=27, expected=0, diff=27
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔥 next_token_logits shape: (1, 152064)
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 KV Cache Status BEFORE: avail=27, expected=0, diff=27
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 batch.out_cache_loc exists: tensor([ 0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17,
        18, 19, 20], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔧 CRITICAL FIX: Freeing KV cache allocated by Prefill instance
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] ✅ CRITICAL FIX: Successfully freed KV cache in Prefill instance
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔥 Creating BatchProcessPrefillResultReq with next_token_ids=[104198]
[2025-07-21 06:55:09 PREFILL TP1] [PREFILL] 🔥 Send response to D worker
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 batch.out_cache_loc exists: tensor([ 0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17,
        18, 19, 20], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔧 CRITICAL FIX: Freeing KV cache allocated by Prefill instance
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] ✅ CRITICAL FIX: Successfully freed KV cache in Prefill instance
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔥 Creating BatchProcessPrefillResultReq with next_token_ids=[104198]
[2025-07-21 06:55:09 PREFILL TP0] [PREFILL] 🔥 Send response to D worker
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔥 process_prefill_result started, next_token_ids=[104198]
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔥 Got batch with 1 requests
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔥 process_prefill_result started, next_token_ids=[104198]
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔥 Got batch with 1 requests
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔥 Setting batch.output_ids...
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔥 Setting batch.output_ids...
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔧 KV Cache Status BEFORE process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔥 Calling process_batch_result_prefill...
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔥 process_batch_result_prefill called for Semi-PD Decode instance
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔧 KV Cache Status BEFORE process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🚨 CRITICAL: Checking weight sharing in Decode instance...
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔥 Calling process_batch_result_prefill...
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔥 process_batch_result_prefill called for Semi-PD Decode instance
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🚨 CRITICAL: Checking weight sharing in Decode instance...
[2025-07-21 06:55:09 DECODE TP0] [DECODE] ❌ Could not find model
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔧 DEBUG: tp_worker type = <class 'sglang.srt.managers.tp_worker_overlap_thread.TpModelWorkerClient'>
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔧 DEBUG: tp_worker attributes = ['cur_sampling_info', 'device', 'forward_batch_generation', 'forward_stream', 'forward_thread', 'forward_thread_func', 'forward_thread_func_', 'future_token_ids_ct', 'future_token_ids_limit', 'future_token_ids_map', 'get_attention_tp_cpu_group', 'get_attention_tp_group', 'get_ipc_info', 'get_kv_cache', 'get_memory_pool', 'get_pad_input_ids_func', 'get_tp_group', 'get_weights_by_name', 'get_worker_info', 'gpu_id', 'hicache_layer_transfer_counter', 'init_attention_backend', 'init_cuda_graphs', 'init_weights_update_group', 'input_queue', 'max_running_requests', 'output_queue', 'parent_process', 'register_hicache_layer_transfer_counter', 'resolve_last_batch_result', 'scheduler_stream', 'set_hicache_consumer', 'share_params_from_ipc', 'update_weights_from_disk', 'update_weights_from_distributed', 'update_weights_from_tensor', 'worker']
[2025-07-21 06:55:09 DECODE TP1] [DECODE] ❌ Could not find model
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔧 DEBUG: Found tp_worker.worker
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔧 DEBUG: tp_worker type = <class 'sglang.srt.managers.tp_worker_overlap_thread.TpModelWorkerClient'>
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔧 DEBUG: Found tp_worker.worker.model_runner
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔧 DEBUG: Found model via tp_worker.worker.model_runner.model
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔧 CRITICAL FIX: Called free_group_begin() for KV Cache management
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔧 DEBUG: tp_worker attributes = ['cur_sampling_info', 'device', 'forward_batch_generation', 'forward_stream', 'forward_thread', 'forward_thread_func', 'forward_thread_func_', 'future_token_ids_ct', 'future_token_ids_limit', 'future_token_ids_map', 'get_attention_tp_cpu_group', 'get_attention_tp_group', 'get_ipc_info', 'get_kv_cache', 'get_memory_pool', 'get_pad_input_ids_func', 'get_tp_group', 'get_weights_by_name', 'get_worker_info', 'gpu_id', 'hicache_layer_transfer_counter', 'init_attention_backend', 'init_cuda_graphs', 'init_weights_update_group', 'input_queue', 'max_running_requests', 'output_queue', 'parent_process', 'register_hicache_layer_transfer_counter', 'resolve_last_batch_result', 'scheduler_stream', 'set_hicache_consumer', 'share_params_from_ipc', 'update_weights_from_disk', 'update_weights_from_distributed', 'update_weights_from_tensor', 'worker']
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔧 DEBUG: Found tp_worker.worker
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔧 DEBUG: Found tp_worker.worker.model_runner
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔧 DEBUG: Found model via tp_worker.worker.model_runner.model
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔧 CRITICAL FIX: Called free_group_begin() for KV Cache management
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔧 DEBUG: Decode batch.out_cache_loc exists: tensor([ 1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
        19, 20, 21], device='cuda:0')
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔧 DEBUG: Decode batch.out_cache_loc exists: tensor([ 1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
        19, 20, 21], device='cuda:1')
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔧 CRITICAL FIX: Called free_group_end() to complete KV Cache management
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔥 process_batch_result_prefill completed for Semi-PD
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔧 KV Cache Status AFTER process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:55:09 DECODE TP0] [DECODE] ⚠️ KV Cache unchanged - potential memory leak!
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔥 process_batch_result_prefill completed!
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔥 Filtering batch...
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔥 Merging batch to running_batch...
[2025-07-21 06:55:09 DECODE TP0] [DECODE] 🔥 process_prefill_result completed successfully!
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔧 CRITICAL FIX: Called free_group_end() to complete KV Cache management
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔥 process_batch_result_prefill completed for Semi-PD
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔧 KV Cache Status AFTER process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:55:09 DECODE TP1] [DECODE] ⚠️ KV Cache unchanged - potential memory leak!
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔥 process_batch_result_prefill completed!
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔥 Filtering batch...
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔥 Merging batch to running_batch...
[2025-07-21 06:55:09 DECODE TP1] [DECODE] 🔥 process_prefill_result completed successfully!
[2025-07-21 06:55:12 DECODE TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 0.00, #queue-req: 0
[2025-07-21 06:55:15 DECODE TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 0.00, #queue-req: 0
[2025-07-21 06:55:18 DECODE TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 0.00, #queue-req: 0
[2025-07-21 06:55:19] INFO:     127.0.0.1:36974 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-21 06:55:19 DECODE TP0] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23722, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23722
self.tree_cache.evictable_size()=0

[2025-07-21 06:55:19 DECODE TP1] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23722, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23722
self.tree_cache.evictable_size()=0

[2025-07-21 06:55:19 DECODE TP0] Cache flushed successfully!
[2025-07-21 06:55:19 DECODE TP1] Cache flushed successfully!
[2025-07-21 06:55:19 DECODE TP0] Memory leak resolved after cache flush
[2025-07-21 06:55:19 DECODE TP1] Memory leak resolved after cache flush
[2025-07-21 06:55:24 DECODE TP0] New request f29aef0729db448dbd117b577af86b86, #tokens: 21
[2025-07-21 06:55:24 DECODE TP0] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:24 DECODE TP1] New request f29aef0729db448dbd117b577af86b86, #tokens: 21
[2025-07-21 06:55:24 PREFILL TP0] New request f29aef0729db448dbd117b577af86b86, #tokens: 21
[2025-07-21 06:55:24 PREFILL TP0] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:24 DECODE TP1] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] Send request to D worker: GetNextPrefillBatchInput(rids=['f29aef0729db448dbd117b577af86b86'])
[2025-07-21 06:55:24 PREFILL TP1] New request f29aef0729db448dbd117b577af86b86, #tokens: 21
[2025-07-21 06:55:24 PREFILL TP1] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:24 DECODE TP0] [DECODE] 🔥 get_next_prefill_batch called with rids: ['f29aef0729db448dbd117b577af86b86']
[2025-07-21 06:55:24 DECODE TP0] [DECODE] 🔧 DEBUG: Request f29aef0729db448dbd117b577af86b86 origin_input_ids = [151644, 8948, 198, 2610, 525, 264, 10950, 17847, 13, 151645, 198, 151644, 872, 198, 105043, 100165, 151645, 198, 151644, 77091, 198]
[2025-07-21 06:55:24 DECODE TP1] [DECODE] 🔥 get_next_prefill_batch called with rids: ['f29aef0729db448dbd117b577af86b86']
[2025-07-21 06:55:24 DECODE TP0] [DECODE] 🔧 DEBUG: Request f29aef0729db448dbd117b577af86b86 origin_input_ids length = 21
[2025-07-21 06:55:24 DECODE TP1] [DECODE] 🔧 DEBUG: Request f29aef0729db448dbd117b577af86b86 origin_input_ids = [151644, 8948, 198, 2610, 525, 264, 10950, 17847, 13, 151645, 198, 151644, 872, 198, 105043, 100165, 151645, 198, 151644, 77091, 198]
[2025-07-21 06:55:24 DECODE TP0] [DECODE] 🔧 DEBUG: Request f29aef0729db448dbd117b577af86b86 last token = 198
[2025-07-21 06:55:24 DECODE TP1] [DECODE] 🔧 DEBUG: Request f29aef0729db448dbd117b577af86b86 origin_input_ids length = 21
[2025-07-21 06:55:24 DECODE TP0] [DECODE] Calling get_new_batch_prefill with rids: ['f29aef0729db448dbd117b577af86b86']
[2025-07-21 06:55:24 DECODE TP1] [DECODE] 🔧 DEBUG: Request f29aef0729db448dbd117b577af86b86 last token = 198
[2025-07-21 06:55:24 DECODE TP1] [DECODE] Calling get_new_batch_prefill with rids: ['f29aef0729db448dbd117b577af86b86']
[2025-07-21 06:55:24 DECODE TP0] [DECODE] Processing waiting queue, rids=['f29aef0729db448dbd117b577af86b86'], waiting_queue_size=1
[2025-07-21 06:55:24 DECODE TP1] [DECODE] Processing waiting queue, rids=['f29aef0729db448dbd117b577af86b86'], waiting_queue_size=1
[2025-07-21 06:55:24 DECODE TP0] Prefill batch. #new-seq: 1, #new-token: 21, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0
[2025-07-21 06:55:24 DECODE TP0] [DECODE] get_new_batch_prefill returned: True
[2025-07-21 06:55:24 DECODE TP0] [DECODE] Send response to P worker: GetNextPrefillBatchOutput(rids=['f29aef0729db448dbd117b577af86b86'], chunked_rid=None, req_pool_indices=[0], prefix_lens=[0], extend_input_lens=[21], finished_reasons=None)
[2025-07-21 06:55:24 DECODE TP1] [DECODE] get_new_batch_prefill returned: True
[2025-07-21 06:55:24 DECODE TP0] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23553, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23553
self.tree_cache.evictable_size()=0

[2025-07-21 06:55:24 DECODE TP1] [DECODE] Send response to P worker: GetNextPrefillBatchOutput(rids=['f29aef0729db448dbd117b577af86b86'], chunked_rid=None, req_pool_indices=[0], prefix_lens=[0], extend_input_lens=[21], finished_reasons=None)
[2025-07-21 06:55:24 DECODE TP1] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23553, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23553
self.tree_cache.evictable_size()=0

[2025-07-21 06:55:24 DECODE TP0] Cache flushed successfully!
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] Recv response from D worker: GetNextPrefillBatchOutput(rids=['f29aef0729db448dbd117b577af86b86'], chunked_rid=None, req_pool_indices=[0], prefix_lens=[0], extend_input_lens=[21], finished_reasons=None)
[2025-07-21 06:55:24 DECODE TP0] Memory leak resolved after cache flush
[2025-07-21 06:55:24 DECODE TP1] Cache flushed successfully!
[2025-07-21 06:55:24 DECODE TP1] Memory leak resolved after cache flush
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] Creating batch from response: ['f29aef0729db448dbd117b577af86b86']
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] Creating batch from response: ['f29aef0729db448dbd117b577af86b86']
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 Request f29aef0729db448dbd117b577af86b86 sampling params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 Request f29aef0729db448dbd117b577af86b86 sampling params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 Batch sampling info: temperatures=tensor([[0.7000]], device='cuda:0'), is_all_greedy=False
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] Successfully created batch with 1 requests
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] get_next_batch_to_run returning batch with 1 requests
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔥 Semi-PD Prefill: Generating logits (no pipeline parallel)
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] ❌ Failed to check embedding output: tuple index out of range
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 Using model.embed_tokens
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 Batch sampling info: temperatures=tensor([[0.7000]], device='cuda:1'), is_all_greedy=False
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] Successfully created batch with 1 requests
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] get_next_batch_to_run returning batch with 1 requests
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔥 Semi-PD Prefill: Generating logits (no pipeline parallel)
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] ❌ Failed to check embedding output: tuple index out of range
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 Using model.embed_tokens
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🚨 EMBEDDING: checksum=-1336.000000, ptr=0x7f6970000000
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🚨 EMBEDDING STATS: mean=-0.000003, std=0.019165, min=-0.769531, max=0.730469
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🚨 EMBEDDING: checksum=3600.000000, ptr=0x7a7f04000000
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🚨 EMBEDDING STATS: mean=0.000009, std=0.015625, min=-0.196289, max=0.183594
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🚨 ZERO RATIO: 0.000000 (5/389283840)
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🚨 TOKEN 15 EMBEDDING: [-0.01141357421875, -0.0035552978515625, -0.0032958984375, -0.00244140625, -0.00958251953125]
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🚨 TOKEN 16 EMBEDDING: [-0.0081787109375, -0.0047607421875, -0.00107574462890625, 0.000896453857421875, 0.002410888671875]
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🚨 TOKEN 108386 EMBEDDING: N/A
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 Using lm_head
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🚨 ZERO RATIO: 0.000000 (56/389283840)
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🚨 TOKEN 15 EMBEDDING: [-0.0091552734375, 0.0025177001953125, 0.009033203125, -0.0166015625, 0.01434326171875]
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🚨 TOKEN 16 EMBEDDING: [-0.00537109375, -0.0086669921875, 0.0106201171875, 0.01318359375, -0.0019378662109375]
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🚨 TOKEN 108386 EMBEDDING: N/A
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 Using lm_head
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🚨 LM_HEAD: checksum=14720.000000, mean=0.000038, std=0.017700
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - vocab_size=152064
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - hidden_size=5120
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - num_layers=64
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - model_type=qwen2
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model training mode = False
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model device = cuda:0
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model dtype = torch.bfloat16
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.temperatures = tensor([[0.7000]], device='cuda:0')
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🚨 LM_HEAD: checksum=11520.000000, mean=0.000030, std=0.017456
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ks = tensor([1073741824], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - vocab_size=152064
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - hidden_size=5120
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - num_layers=64
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - model_type=qwen2
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model training mode = False
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model device = cuda:1
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model dtype = torch.bfloat16
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ps = tensor([1.], device='cuda:0')
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.is_all_greedy = False
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: Original is_all_greedy=False, temperature=0.699999988079071
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.temperatures = tensor([[0.7000]], device='cuda:1')
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ks = tensor([1073741824], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ps = tensor([1.], device='cuda:1')
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.is_all_greedy = False
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: Original is_all_greedy=False, temperature=0.699999988079071
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 VL Model: Extracted text components from tuple result
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits shape = torch.Size([1, 152064])
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits dtype = torch.float32
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits device = cuda:0
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 VL Model: Extracted text components from tuple result
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits shape = torch.Size([1, 152064])
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits dtype = torch.float32
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits device = cuda:1
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits stats: mean=0.000007, std=0.002564, min=0.000000, max=0.999989
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits stats: mean=0.000007, std=0.002564, min=0.000000, max=0.999989
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits anomalies: nan_count=0, inf_count=0
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits anomalies: nan_count=0, inf_count=0
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: top-5 logits = [0.9999891519546509, 1.0879998626478482e-05, 1.4421160932087673e-09, 1.4151962934860762e-10, 8.28244625661334e-11]
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: top-5 indices = [104198, 35946, 111308, 108386, 128296]
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: top-5 logits = [0.9999891519546509, 1.0879998626478482e-05, 1.4421160932087673e-09, 1.4151962934860762e-10, 8.28244625661334e-11]
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: token 16 logit value = 9.311456350223675e-19
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: token 108386 logit value = 1.4151962934860762e-10
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: top-5 indices = [104198, 35946, 111308, 108386, 128296]
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: token 16 logit value = 9.311456350223675e-19
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: token 108386 logit value = 1.4151962934860762e-10
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: Generated next_token_ids = tensor([104198], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 DEBUG: next_token_ids type = <class 'torch.Tensor'>
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: Generated next_token_ids = tensor([104198], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔥 Generated logits and token IDs: tensor([104198], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 DEBUG: next_token_ids type = <class 'torch.Tensor'>
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔥 process_batch_result_prefill called with result.next_token_ids=tensor([104198], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔥 result type: <class 'sglang.srt.managers.scheduler.GenerationBatchResult'>
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔥 Generated logits and token IDs: tensor([104198], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔥 result.logits_output: True
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔥 Processing req f29aef0729db448dbd117b577af86b86, next_token_id=104198
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔥 Added token 104198 to req f29aef0729db448dbd117b577af86b86, output_ids=[104198]
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔥 Request f29aef0729db448dbd117b577af86b86 continuing (skipping cache operations)
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔥 process_batch_result_prefill called with result.next_token_ids=tensor([104198], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔥 result type: <class 'sglang.srt.managers.scheduler.GenerationBatchResult'>
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔥 next_token_logits shape: (1, 152064)
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔥 result.logits_output: True
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 KV Cache Status BEFORE: avail=48, expected=0, diff=48
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔥 Processing req f29aef0729db448dbd117b577af86b86, next_token_id=104198
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔥 Added token 104198 to req f29aef0729db448dbd117b577af86b86, output_ids=[104198]
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔥 Request f29aef0729db448dbd117b577af86b86 continuing (skipping cache operations)
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 batch.out_cache_loc exists: tensor([ 0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17,
        18, 19, 20], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔥 next_token_logits shape: (1, 152064)
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔧 CRITICAL FIX: Freeing KV cache allocated by Prefill instance
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 KV Cache Status BEFORE: avail=48, expected=0, diff=48
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] ✅ CRITICAL FIX: Successfully freed KV cache in Prefill instance
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔥 Creating BatchProcessPrefillResultReq with next_token_ids=[104198]
[2025-07-21 06:55:24 PREFILL TP0] [PREFILL] 🔥 Send response to D worker
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 batch.out_cache_loc exists: tensor([ 0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17,
        18, 19, 20], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔧 CRITICAL FIX: Freeing KV cache allocated by Prefill instance
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] ✅ CRITICAL FIX: Successfully freed KV cache in Prefill instance
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔥 Creating BatchProcessPrefillResultReq with next_token_ids=[104198]
[2025-07-21 06:55:24 PREFILL TP1] [PREFILL] 🔥 Send response to D worker
[2025-07-21 06:55:24 DECODE TP0] [DECODE] 🔥 process_prefill_result started, next_token_ids=[104198]
[2025-07-21 06:55:24 DECODE TP0] [DECODE] 🔥 Got batch with 1 requests
[2025-07-21 06:55:24 DECODE TP1] [DECODE] 🔥 process_prefill_result started, next_token_ids=[104198]
[2025-07-21 06:55:24 DECODE TP1] [DECODE] 🔥 Got batch with 1 requests
[2025-07-21 06:55:25 DECODE TP0] [DECODE] 🔥 Setting batch.output_ids...
[2025-07-21 06:55:25 DECODE TP1] [DECODE] 🔥 Setting batch.output_ids...
[2025-07-21 06:55:25 DECODE TP0] [DECODE] 🔧 KV Cache Status BEFORE process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:55:25 DECODE TP0] [DECODE] 🔥 Calling process_batch_result_prefill...
[2025-07-21 06:55:25 DECODE TP1] [DECODE] 🔧 KV Cache Status BEFORE process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:55:25 DECODE TP0] [DECODE] 🔥 process_batch_result_prefill called for Semi-PD Decode instance
[2025-07-21 06:55:25 DECODE TP1] [DECODE] 🔥 Calling process_batch_result_prefill...
[2025-07-21 06:55:25 DECODE TP0] [DECODE] 🚨 CRITICAL: Checking weight sharing in Decode instance...
[2025-07-21 06:55:25 DECODE TP1] [DECODE] 🔥 process_batch_result_prefill called for Semi-PD Decode instance
[2025-07-21 06:55:25 DECODE TP1] [DECODE] 🚨 CRITICAL: Checking weight sharing in Decode instance...
[2025-07-21 06:55:25 DECODE TP0] [DECODE] ❌ Could not find model
[2025-07-21 06:55:25 DECODE TP0] [DECODE] 🔧 DEBUG: tp_worker type = <class 'sglang.srt.managers.tp_worker_overlap_thread.TpModelWorkerClient'>
[2025-07-21 06:55:25 DECODE TP1] [DECODE] ❌ Could not find model
[2025-07-21 06:55:25 DECODE TP1] [DECODE] 🔧 DEBUG: tp_worker type = <class 'sglang.srt.managers.tp_worker_overlap_thread.TpModelWorkerClient'>
[2025-07-21 06:55:25 DECODE TP0] [DECODE] 🔧 DEBUG: tp_worker attributes = ['cur_sampling_info', 'device', 'forward_batch_generation', 'forward_stream', 'forward_thread', 'forward_thread_func', 'forward_thread_func_', 'future_token_ids_ct', 'future_token_ids_limit', 'future_token_ids_map', 'get_attention_tp_cpu_group', 'get_attention_tp_group', 'get_ipc_info', 'get_kv_cache', 'get_memory_pool', 'get_pad_input_ids_func', 'get_tp_group', 'get_weights_by_name', 'get_worker_info', 'gpu_id', 'hicache_layer_transfer_counter', 'init_attention_backend', 'init_cuda_graphs', 'init_weights_update_group', 'input_queue', 'max_running_requests', 'output_queue', 'parent_process', 'register_hicache_layer_transfer_counter', 'resolve_last_batch_result', 'scheduler_stream', 'set_hicache_consumer', 'share_params_from_ipc', 'update_weights_from_disk', 'update_weights_from_distributed', 'update_weights_from_tensor', 'worker']
[2025-07-21 06:55:25 DECODE TP0] [DECODE] 🔧 DEBUG: Found tp_worker.worker
[2025-07-21 06:55:25 DECODE TP0] [DECODE] 🔧 DEBUG: Found tp_worker.worker.model_runner
[2025-07-21 06:55:25 DECODE TP1] [DECODE] 🔧 DEBUG: tp_worker attributes = ['cur_sampling_info', 'device', 'forward_batch_generation', 'forward_stream', 'forward_thread', 'forward_thread_func', 'forward_thread_func_', 'future_token_ids_ct', 'future_token_ids_limit', 'future_token_ids_map', 'get_attention_tp_cpu_group', 'get_attention_tp_group', 'get_ipc_info', 'get_kv_cache', 'get_memory_pool', 'get_pad_input_ids_func', 'get_tp_group', 'get_weights_by_name', 'get_worker_info', 'gpu_id', 'hicache_layer_transfer_counter', 'init_attention_backend', 'init_cuda_graphs', 'init_weights_update_group', 'input_queue', 'max_running_requests', 'output_queue', 'parent_process', 'register_hicache_layer_transfer_counter', 'resolve_last_batch_result', 'scheduler_stream', 'set_hicache_consumer', 'share_params_from_ipc', 'update_weights_from_disk', 'update_weights_from_distributed', 'update_weights_from_tensor', 'worker']
[2025-07-21 06:55:25 DECODE TP0] [DECODE] 🔧 DEBUG: Found model via tp_worker.worker.model_runner.model
[2025-07-21 06:55:25 DECODE TP1] [DECODE] 🔧 DEBUG: Found tp_worker.worker
[2025-07-21 06:55:25 DECODE TP0] [DECODE] 🔧 CRITICAL FIX: Called free_group_begin() for KV Cache management
[2025-07-21 06:55:25 DECODE TP1] [DECODE] 🔧 DEBUG: Found tp_worker.worker.model_runner
[2025-07-21 06:55:25 DECODE TP1] [DECODE] 🔧 DEBUG: Found model via tp_worker.worker.model_runner.model
[2025-07-21 06:55:25 DECODE TP1] [DECODE] 🔧 CRITICAL FIX: Called free_group_begin() for KV Cache management
[2025-07-21 06:55:25 DECODE TP0] [DECODE] 🔧 DEBUG: Decode batch.out_cache_loc exists: tensor([ 1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
        19, 20, 21], device='cuda:0')
[2025-07-21 06:55:25 DECODE TP1] [DECODE] 🔧 DEBUG: Decode batch.out_cache_loc exists: tensor([ 1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
        19, 20, 21], device='cuda:1')
[2025-07-21 06:55:25 DECODE TP0] [DECODE] 🔧 CRITICAL FIX: Called free_group_end() to complete KV Cache management
[2025-07-21 06:55:25 DECODE TP0] [DECODE] 🔥 process_batch_result_prefill completed for Semi-PD
[2025-07-21 06:55:25 DECODE TP0] [DECODE] 🔧 KV Cache Status AFTER process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:55:25 DECODE TP0] [DECODE] ⚠️ KV Cache unchanged - potential memory leak!
[2025-07-21 06:55:25 DECODE TP0] [DECODE] 🔥 process_batch_result_prefill completed!
[2025-07-21 06:55:25 DECODE TP0] [DECODE] 🔥 Filtering batch...
[2025-07-21 06:55:25 DECODE TP0] [DECODE] 🔥 Merging batch to running_batch...
[2025-07-21 06:55:25 DECODE TP0] [DECODE] 🔥 process_prefill_result completed successfully!
[2025-07-21 06:55:25 DECODE TP1] [DECODE] 🔧 CRITICAL FIX: Called free_group_end() to complete KV Cache management
[2025-07-21 06:55:25 DECODE TP1] [DECODE] 🔥 process_batch_result_prefill completed for Semi-PD
[2025-07-21 06:55:25 DECODE TP1] [DECODE] 🔧 KV Cache Status AFTER process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:55:25 DECODE TP1] [DECODE] ⚠️ KV Cache unchanged - potential memory leak!
[2025-07-21 06:55:25 DECODE TP1] [DECODE] 🔥 process_batch_result_prefill completed!
[2025-07-21 06:55:25 DECODE TP1] [DECODE] 🔥 Filtering batch...
[2025-07-21 06:55:25 DECODE TP1] [DECODE] 🔥 Merging batch to running_batch...
[2025-07-21 06:55:25 DECODE TP1] [DECODE] 🔥 process_prefill_result completed successfully!
[2025-07-21 06:55:26] INFO:     127.0.0.1:39374 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-21 06:55:26 DECODE TP0] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23612, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23612
self.tree_cache.evictable_size()=0

[2025-07-21 06:55:26 DECODE TP1] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23612, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23612
self.tree_cache.evictable_size()=0

[2025-07-21 06:55:26 DECODE TP0] Cache flushed successfully!
[2025-07-21 06:55:26 DECODE TP1] Cache flushed successfully!
[2025-07-21 06:55:26 DECODE TP0] Memory leak resolved after cache flush
[2025-07-21 06:55:26 DECODE TP1] Memory leak resolved after cache flush
[2025-07-21 06:55:28 DECODE TP0] New request 88fb38786ac84e29b6edebe44887a7b0, #tokens: 21
[2025-07-21 06:55:28 DECODE TP0] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:28 PREFILL TP0] New request 88fb38786ac84e29b6edebe44887a7b0, #tokens: 21
[2025-07-21 06:55:28 DECODE TP1] New request 88fb38786ac84e29b6edebe44887a7b0, #tokens: 21
[2025-07-21 06:55:28 PREFILL TP0] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:28 DECODE TP1] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:28 PREFILL TP1] New request 88fb38786ac84e29b6edebe44887a7b0, #tokens: 21
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] Send request to D worker: GetNextPrefillBatchInput(rids=['88fb38786ac84e29b6edebe44887a7b0'])
[2025-07-21 06:55:28 PREFILL TP1] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔥 get_next_prefill_batch called with rids: ['88fb38786ac84e29b6edebe44887a7b0']
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔥 get_next_prefill_batch called with rids: ['88fb38786ac84e29b6edebe44887a7b0']
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔧 DEBUG: Request 88fb38786ac84e29b6edebe44887a7b0 origin_input_ids = [151644, 8948, 198, 2610, 525, 264, 10950, 17847, 13, 151645, 198, 151644, 872, 198, 105043, 100165, 151645, 198, 151644, 77091, 198]
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔧 DEBUG: Request 88fb38786ac84e29b6edebe44887a7b0 origin_input_ids length = 21
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔧 DEBUG: Request 88fb38786ac84e29b6edebe44887a7b0 origin_input_ids = [151644, 8948, 198, 2610, 525, 264, 10950, 17847, 13, 151645, 198, 151644, 872, 198, 105043, 100165, 151645, 198, 151644, 77091, 198]
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔧 DEBUG: Request 88fb38786ac84e29b6edebe44887a7b0 last token = 198
[2025-07-21 06:55:28 DECODE TP0] [DECODE] Calling get_new_batch_prefill with rids: ['88fb38786ac84e29b6edebe44887a7b0']
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔧 DEBUG: Request 88fb38786ac84e29b6edebe44887a7b0 origin_input_ids length = 21
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔧 DEBUG: Request 88fb38786ac84e29b6edebe44887a7b0 last token = 198
[2025-07-21 06:55:28 DECODE TP0] [DECODE] Processing waiting queue, rids=['88fb38786ac84e29b6edebe44887a7b0'], waiting_queue_size=1
[2025-07-21 06:55:28 DECODE TP1] [DECODE] Calling get_new_batch_prefill with rids: ['88fb38786ac84e29b6edebe44887a7b0']
[2025-07-21 06:55:28 DECODE TP1] [DECODE] Processing waiting queue, rids=['88fb38786ac84e29b6edebe44887a7b0'], waiting_queue_size=1
[2025-07-21 06:55:28 DECODE TP0] Prefill batch. #new-seq: 1, #new-token: 21, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0
[2025-07-21 06:55:28 DECODE TP0] [DECODE] get_new_batch_prefill returned: True
[2025-07-21 06:55:28 DECODE TP0] [DECODE] Send response to P worker: GetNextPrefillBatchOutput(rids=['88fb38786ac84e29b6edebe44887a7b0'], chunked_rid=None, req_pool_indices=[0], prefix_lens=[0], extend_input_lens=[21], finished_reasons=None)
[2025-07-21 06:55:28 DECODE TP0] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23553, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23553
self.tree_cache.evictable_size()=0

[2025-07-21 06:55:28 DECODE TP1] [DECODE] get_new_batch_prefill returned: True
[2025-07-21 06:55:28 DECODE TP1] [DECODE] Send response to P worker: GetNextPrefillBatchOutput(rids=['88fb38786ac84e29b6edebe44887a7b0'], chunked_rid=None, req_pool_indices=[0], prefix_lens=[0], extend_input_lens=[21], finished_reasons=None)
[2025-07-21 06:55:28 DECODE TP0] Cache flushed successfully!
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] Recv response from D worker: GetNextPrefillBatchOutput(rids=['88fb38786ac84e29b6edebe44887a7b0'], chunked_rid=None, req_pool_indices=[0], prefix_lens=[0], extend_input_lens=[21], finished_reasons=None)
[2025-07-21 06:55:28 DECODE TP1] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23553, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23553
self.tree_cache.evictable_size()=0

[2025-07-21 06:55:28 DECODE TP0] Memory leak resolved after cache flush
[2025-07-21 06:55:28 DECODE TP1] Cache flushed successfully!
[2025-07-21 06:55:28 DECODE TP1] Memory leak resolved after cache flush
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] Creating batch from response: ['88fb38786ac84e29b6edebe44887a7b0']
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] Creating batch from response: ['88fb38786ac84e29b6edebe44887a7b0']
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 Request 88fb38786ac84e29b6edebe44887a7b0 sampling params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 Request 88fb38786ac84e29b6edebe44887a7b0 sampling params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 Batch sampling info: temperatures=tensor([[0.7000]], device='cuda:1'), is_all_greedy=False
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 Batch sampling info: temperatures=tensor([[0.7000]], device='cuda:0'), is_all_greedy=False
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] Successfully created batch with 1 requests
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] Successfully created batch with 1 requests
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] get_next_batch_to_run returning batch with 1 requests
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] get_next_batch_to_run returning batch with 1 requests
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔥 Semi-PD Prefill: Generating logits (no pipeline parallel)
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔥 Semi-PD Prefill: Generating logits (no pipeline parallel)
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] ❌ Failed to check embedding output: tuple index out of range
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] ❌ Failed to check embedding output: tuple index out of range
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 Using model.embed_tokens
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 Using model.embed_tokens
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🚨 EMBEDDING: checksum=-1336.000000, ptr=0x7f6970000000
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🚨 EMBEDDING: checksum=3600.000000, ptr=0x7a7f04000000
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🚨 EMBEDDING STATS: mean=-0.000003, std=0.019165, min=-0.769531, max=0.730469
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🚨 EMBEDDING STATS: mean=0.000009, std=0.015625, min=-0.196289, max=0.183594
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🚨 ZERO RATIO: 0.000000 (5/389283840)
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🚨 ZERO RATIO: 0.000000 (56/389283840)
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🚨 TOKEN 15 EMBEDDING: [-0.01141357421875, -0.0035552978515625, -0.0032958984375, -0.00244140625, -0.00958251953125]
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🚨 TOKEN 16 EMBEDDING: [-0.0081787109375, -0.0047607421875, -0.00107574462890625, 0.000896453857421875, 0.002410888671875]
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🚨 TOKEN 15 EMBEDDING: [-0.0091552734375, 0.0025177001953125, 0.009033203125, -0.0166015625, 0.01434326171875]
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🚨 TOKEN 108386 EMBEDDING: N/A
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 Using lm_head
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🚨 TOKEN 16 EMBEDDING: [-0.00537109375, -0.0086669921875, 0.0106201171875, 0.01318359375, -0.0019378662109375]
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🚨 TOKEN 108386 EMBEDDING: N/A
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 Using lm_head
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🚨 LM_HEAD: checksum=14720.000000, mean=0.000038, std=0.017700
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - vocab_size=152064
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - hidden_size=5120
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🚨 LM_HEAD: checksum=11520.000000, mean=0.000030, std=0.017456
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - num_layers=64
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - model_type=qwen2
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model training mode = False
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - vocab_size=152064
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - hidden_size=5120
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model device = cuda:0
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - num_layers=64
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model dtype = torch.bfloat16
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - model_type=qwen2
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model training mode = False
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model device = cuda:1
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model dtype = torch.bfloat16
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.temperatures = tensor([[0.7000]], device='cuda:0')
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ks = tensor([1073741824], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.temperatures = tensor([[0.7000]], device='cuda:1')
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ks = tensor([1073741824], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ps = tensor([1.], device='cuda:0')
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.is_all_greedy = False
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: Original is_all_greedy=False, temperature=0.699999988079071
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ps = tensor([1.], device='cuda:1')
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.is_all_greedy = False
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: Original is_all_greedy=False, temperature=0.699999988079071
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 VL Model: Extracted text components from tuple result
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits shape = torch.Size([1, 152064])
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits dtype = torch.float32
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits device = cuda:0
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 VL Model: Extracted text components from tuple result
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits shape = torch.Size([1, 152064])
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits dtype = torch.float32
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits device = cuda:1
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits stats: mean=0.000007, std=0.002564, min=0.000000, max=0.999989
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits stats: mean=0.000007, std=0.002564, min=0.000000, max=0.999989
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits anomalies: nan_count=0, inf_count=0
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits anomalies: nan_count=0, inf_count=0
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: top-5 logits = [0.9999891519546509, 1.0879998626478482e-05, 1.4421160932087673e-09, 1.4151962934860762e-10, 8.28244625661334e-11]
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: top-5 logits = [0.9999891519546509, 1.0879998626478482e-05, 1.4421160932087673e-09, 1.4151962934860762e-10, 8.28244625661334e-11]
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: top-5 indices = [104198, 35946, 111308, 108386, 128296]
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: top-5 indices = [104198, 35946, 111308, 108386, 128296]
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: token 16 logit value = 9.311456350223675e-19
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: token 108386 logit value = 1.4151962934860762e-10
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: token 16 logit value = 9.311456350223675e-19
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: token 108386 logit value = 1.4151962934860762e-10
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: Generated next_token_ids = tensor([104198], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 DEBUG: next_token_ids type = <class 'torch.Tensor'>
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: Generated next_token_ids = tensor([104198], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 DEBUG: next_token_ids type = <class 'torch.Tensor'>
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔥 Generated logits and token IDs: tensor([104198], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔥 process_batch_result_prefill called with result.next_token_ids=tensor([104198], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔥 Generated logits and token IDs: tensor([104198], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔥 result type: <class 'sglang.srt.managers.scheduler.GenerationBatchResult'>
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔥 result.logits_output: True
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔥 Processing req 88fb38786ac84e29b6edebe44887a7b0, next_token_id=104198
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔥 Added token 104198 to req 88fb38786ac84e29b6edebe44887a7b0, output_ids=[104198]
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔥 Request 88fb38786ac84e29b6edebe44887a7b0 continuing (skipping cache operations)
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔥 process_batch_result_prefill called with result.next_token_ids=tensor([104198], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔥 result type: <class 'sglang.srt.managers.scheduler.GenerationBatchResult'>
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔥 result.logits_output: True
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔥 next_token_logits shape: (1, 152064)
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔥 Processing req 88fb38786ac84e29b6edebe44887a7b0, next_token_id=104198
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 KV Cache Status BEFORE: avail=69, expected=0, diff=69
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔥 Added token 104198 to req 88fb38786ac84e29b6edebe44887a7b0, output_ids=[104198]
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔥 Request 88fb38786ac84e29b6edebe44887a7b0 continuing (skipping cache operations)
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔥 next_token_logits shape: (1, 152064)
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 KV Cache Status BEFORE: avail=69, expected=0, diff=69
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 batch.out_cache_loc exists: tensor([ 0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17,
        18, 19, 20], device='cuda:0', dtype=torch.int32)
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔧 CRITICAL FIX: Freeing KV cache allocated by Prefill instance
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] ✅ CRITICAL FIX: Successfully freed KV cache in Prefill instance
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔥 Creating BatchProcessPrefillResultReq with next_token_ids=[104198]
[2025-07-21 06:55:28 PREFILL TP0] [PREFILL] 🔥 Send response to D worker
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 batch.out_cache_loc exists: tensor([ 0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17,
        18, 19, 20], device='cuda:1', dtype=torch.int32)
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔧 CRITICAL FIX: Freeing KV cache allocated by Prefill instance
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] ✅ CRITICAL FIX: Successfully freed KV cache in Prefill instance
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔥 Creating BatchProcessPrefillResultReq with next_token_ids=[104198]
[2025-07-21 06:55:28 PREFILL TP1] [PREFILL] 🔥 Send response to D worker
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔥 process_prefill_result started, next_token_ids=[104198]
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔥 Got batch with 1 requests
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔥 process_prefill_result started, next_token_ids=[104198]
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔥 Got batch with 1 requests
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔥 Setting batch.output_ids...
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔥 Setting batch.output_ids...
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔧 KV Cache Status BEFORE process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔧 KV Cache Status BEFORE process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔥 Calling process_batch_result_prefill...
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔥 process_batch_result_prefill called for Semi-PD Decode instance
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔥 Calling process_batch_result_prefill...
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🚨 CRITICAL: Checking weight sharing in Decode instance...
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔥 process_batch_result_prefill called for Semi-PD Decode instance
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🚨 CRITICAL: Checking weight sharing in Decode instance...
[2025-07-21 06:55:28 DECODE TP0] [DECODE] ❌ Could not find model
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔧 DEBUG: tp_worker type = <class 'sglang.srt.managers.tp_worker_overlap_thread.TpModelWorkerClient'>
[2025-07-21 06:55:28 DECODE TP1] [DECODE] ❌ Could not find model
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔧 DEBUG: tp_worker attributes = ['cur_sampling_info', 'device', 'forward_batch_generation', 'forward_stream', 'forward_thread', 'forward_thread_func', 'forward_thread_func_', 'future_token_ids_ct', 'future_token_ids_limit', 'future_token_ids_map', 'get_attention_tp_cpu_group', 'get_attention_tp_group', 'get_ipc_info', 'get_kv_cache', 'get_memory_pool', 'get_pad_input_ids_func', 'get_tp_group', 'get_weights_by_name', 'get_worker_info', 'gpu_id', 'hicache_layer_transfer_counter', 'init_attention_backend', 'init_cuda_graphs', 'init_weights_update_group', 'input_queue', 'max_running_requests', 'output_queue', 'parent_process', 'register_hicache_layer_transfer_counter', 'resolve_last_batch_result', 'scheduler_stream', 'set_hicache_consumer', 'share_params_from_ipc', 'update_weights_from_disk', 'update_weights_from_distributed', 'update_weights_from_tensor', 'worker']
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔧 DEBUG: tp_worker type = <class 'sglang.srt.managers.tp_worker_overlap_thread.TpModelWorkerClient'>
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔧 DEBUG: Found tp_worker.worker
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔧 DEBUG: Found tp_worker.worker.model_runner
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔧 DEBUG: Found model via tp_worker.worker.model_runner.model
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔧 DEBUG: tp_worker attributes = ['cur_sampling_info', 'device', 'forward_batch_generation', 'forward_stream', 'forward_thread', 'forward_thread_func', 'forward_thread_func_', 'future_token_ids_ct', 'future_token_ids_limit', 'future_token_ids_map', 'get_attention_tp_cpu_group', 'get_attention_tp_group', 'get_ipc_info', 'get_kv_cache', 'get_memory_pool', 'get_pad_input_ids_func', 'get_tp_group', 'get_weights_by_name', 'get_worker_info', 'gpu_id', 'hicache_layer_transfer_counter', 'init_attention_backend', 'init_cuda_graphs', 'init_weights_update_group', 'input_queue', 'max_running_requests', 'output_queue', 'parent_process', 'register_hicache_layer_transfer_counter', 'resolve_last_batch_result', 'scheduler_stream', 'set_hicache_consumer', 'share_params_from_ipc', 'update_weights_from_disk', 'update_weights_from_distributed', 'update_weights_from_tensor', 'worker']
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔧 CRITICAL FIX: Called free_group_begin() for KV Cache management
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔧 DEBUG: Found tp_worker.worker
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔧 DEBUG: Found tp_worker.worker.model_runner
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔧 DEBUG: Found model via tp_worker.worker.model_runner.model
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔧 CRITICAL FIX: Called free_group_begin() for KV Cache management
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔧 DEBUG: Decode batch.out_cache_loc exists: tensor([ 1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
        19, 20, 21], device='cuda:0')
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔧 DEBUG: Decode batch.out_cache_loc exists: tensor([ 1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
        19, 20, 21], device='cuda:1')
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔧 CRITICAL FIX: Called free_group_end() to complete KV Cache management
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔥 process_batch_result_prefill completed for Semi-PD
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔧 KV Cache Status AFTER process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:55:28 DECODE TP0] [DECODE] ⚠️ KV Cache unchanged - potential memory leak!
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔥 process_batch_result_prefill completed!
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔥 Filtering batch...
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔥 Merging batch to running_batch...
[2025-07-21 06:55:28 DECODE TP0] [DECODE] 🔥 process_prefill_result completed successfully!
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔧 CRITICAL FIX: Called free_group_end() to complete KV Cache management
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔥 process_batch_result_prefill completed for Semi-PD
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔧 KV Cache Status AFTER process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 06:55:28 DECODE TP1] [DECODE] ⚠️ KV Cache unchanged - potential memory leak!
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔥 process_batch_result_prefill completed!
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔥 Filtering batch...
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔥 Merging batch to running_batch...
[2025-07-21 06:55:28 DECODE TP1] [DECODE] 🔥 process_prefill_result completed successfully!
[2025-07-21 06:55:31 DECODE TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 0.00, #queue-req: 0
[2025-07-21 06:55:34 DECODE TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 0.00, #queue-req: 0
[2025-07-21 06:55:37 DECODE TP0] Decode batch. #running-req: 1, #token: 1, token usage: 0.00, cuda graph: True, gen throughput (token/s): 0.00, #queue-req: 0
[2025-07-21 06:55:38] INFO:     127.0.0.1:36798 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-21 06:55:38 DECODE TP0] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23722, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23722
self.tree_cache.evictable_size()=0

[2025-07-21 06:55:38 DECODE TP1] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23722, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23722
self.tree_cache.evictable_size()=0

[2025-07-21 06:55:38 DECODE TP0] Cache flushed successfully!
[2025-07-21 06:55:38 DECODE TP0] Memory leak resolved after cache flush
[2025-07-21 06:55:38 DECODE TP1] Cache flushed successfully!
[2025-07-21 06:55:38 DECODE TP1] Memory leak resolved after cache flush
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   [2025-07-21 07:09:52 PREFILL TP1] New request b5da5f8081b24d469e1656fbdc0a88c7, #tokens: 21
[2025-07-21 07:09:52 PREFILL TP1] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 07:09:52 PREFILL TP0] New request b5da5f8081b24d469e1656fbdc0a88c7, #tokens: 21
[2025-07-21 07:09:52 DECODE TP1] New request b5da5f8081b24d469e1656fbdc0a88c7, #tokens: 21
[2025-07-21 07:09:52 DECODE TP1] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 07:09:52 PREFILL TP0] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 07:09:52 DECODE TP0] New request b5da5f8081b24d469e1656fbdc0a88c7, #tokens: 21
[2025-07-21 07:09:52 DECODE TP0] [SEMI_PD] 🔧 Received sampling_params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] Send request to D worker: GetNextPrefillBatchInput(rids=['b5da5f8081b24d469e1656fbdc0a88c7'])
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔥 get_next_prefill_batch called with rids: ['b5da5f8081b24d469e1656fbdc0a88c7']
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔥 get_next_prefill_batch called with rids: ['b5da5f8081b24d469e1656fbdc0a88c7']
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔧 DEBUG: Request b5da5f8081b24d469e1656fbdc0a88c7 origin_input_ids = [151644, 8948, 198, 2610, 525, 264, 10950, 17847, 13, 151645, 198, 151644, 872, 198, 105043, 100165, 151645, 198, 151644, 77091, 198]
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔧 DEBUG: Request b5da5f8081b24d469e1656fbdc0a88c7 origin_input_ids length = 21
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔧 DEBUG: Request b5da5f8081b24d469e1656fbdc0a88c7 origin_input_ids = [151644, 8948, 198, 2610, 525, 264, 10950, 17847, 13, 151645, 198, 151644, 872, 198, 105043, 100165, 151645, 198, 151644, 77091, 198]
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔧 DEBUG: Request b5da5f8081b24d469e1656fbdc0a88c7 last token = 198
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔧 DEBUG: Request b5da5f8081b24d469e1656fbdc0a88c7 origin_input_ids length = 21
[2025-07-21 07:09:52 DECODE TP1] [DECODE] Calling get_new_batch_prefill with rids: ['b5da5f8081b24d469e1656fbdc0a88c7']
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔧 DEBUG: Request b5da5f8081b24d469e1656fbdc0a88c7 last token = 198
[2025-07-21 07:09:52 DECODE TP0] [DECODE] Calling get_new_batch_prefill with rids: ['b5da5f8081b24d469e1656fbdc0a88c7']
[2025-07-21 07:09:52 DECODE TP1] [DECODE] Processing waiting queue, rids=['b5da5f8081b24d469e1656fbdc0a88c7'], waiting_queue_size=1
[2025-07-21 07:09:52 DECODE TP0] [DECODE] Processing waiting queue, rids=['b5da5f8081b24d469e1656fbdc0a88c7'], waiting_queue_size=1
[2025-07-21 07:09:52 DECODE TP0] Prefill batch. #new-seq: 1, #new-token: 21, #cached-token: 0, token usage: 0.00, #running-req: 0, #queue-req: 0
[2025-07-21 07:09:52 DECODE TP1] [DECODE] get_new_batch_prefill returned: True
[2025-07-21 07:09:52 DECODE TP1] [DECODE] Send response to P worker: GetNextPrefillBatchOutput(rids=['b5da5f8081b24d469e1656fbdc0a88c7'], chunked_rid=None, req_pool_indices=[0], prefix_lens=[0], extend_input_lens=[21], finished_reasons=None)
[2025-07-21 07:09:52 DECODE TP1] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23553, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23553
self.tree_cache.evictable_size()=0

[2025-07-21 07:09:52 DECODE TP1] Cache flushed successfully!
[2025-07-21 07:09:52 DECODE TP1] Memory leak resolved after cache flush
[2025-07-21 07:09:52 DECODE TP0] [DECODE] get_new_batch_prefill returned: True
[2025-07-21 07:09:52 DECODE TP0] [DECODE] Send response to P worker: GetNextPrefillBatchOutput(rids=['b5da5f8081b24d469e1656fbdc0a88c7'], chunked_rid=None, req_pool_indices=[0], prefix_lens=[0], extend_input_lens=[21], finished_reasons=None)
[2025-07-21 07:09:52 DECODE TP0] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23553, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23553
self.tree_cache.evictable_size()=0

[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] Recv response from D worker: GetNextPrefillBatchOutput(rids=['b5da5f8081b24d469e1656fbdc0a88c7'], chunked_rid=None, req_pool_indices=[0], prefix_lens=[0], extend_input_lens=[21], finished_reasons=None)
[2025-07-21 07:09:52 DECODE TP0] Cache flushed successfully!
[2025-07-21 07:09:52 DECODE TP0] Memory leak resolved after cache flush
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] Creating batch from response: ['b5da5f8081b24d469e1656fbdc0a88c7']
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] Creating batch from response: ['b5da5f8081b24d469e1656fbdc0a88c7']
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 Request b5da5f8081b24d469e1656fbdc0a88c7 sampling params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 Request b5da5f8081b24d469e1656fbdc0a88c7 sampling params: temperature=0.7, top_k=1073741824, top_p=1.0
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 Batch sampling info: temperatures=tensor([[0.7000]], device='cuda:1'), is_all_greedy=False
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] Successfully created batch with 1 requests
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] get_next_batch_to_run returning batch with 1 requests
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔥 Semi-PD Prefill: Generating logits (no pipeline parallel)
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] ❌ Failed to check embedding output: tuple index out of range
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 Using model.embed_tokens
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 Batch sampling info: temperatures=tensor([[0.7000]], device='cuda:0'), is_all_greedy=False
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] Successfully created batch with 1 requests
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] get_next_batch_to_run returning batch with 1 requests
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔥 Semi-PD Prefill: Generating logits (no pipeline parallel)
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] ❌ Failed to check embedding output: tuple index out of range
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 Using model.embed_tokens
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🚨 EMBEDDING: checksum=3600.000000, ptr=0x7a7f04000000
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🚨 EMBEDDING STATS: mean=0.000009, std=0.015625, min=-0.196289, max=0.183594
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🚨 EMBEDDING: checksum=-1336.000000, ptr=0x7f6970000000
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🚨 EMBEDDING STATS: mean=-0.000003, std=0.019165, min=-0.769531, max=0.730469
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🚨 ZERO RATIO: 0.000000 (56/389283840)
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🚨 TOKEN 15 EMBEDDING: [-0.0091552734375, 0.0025177001953125, 0.009033203125, -0.0166015625, 0.01434326171875]
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🚨 TOKEN 16 EMBEDDING: [-0.00537109375, -0.0086669921875, 0.0106201171875, 0.01318359375, -0.0019378662109375]
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🚨 TOKEN 108386 EMBEDDING: N/A
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 Using lm_head
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🚨 ZERO RATIO: 0.000000 (5/389283840)
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🚨 TOKEN 15 EMBEDDING: [-0.01141357421875, -0.0035552978515625, -0.0032958984375, -0.00244140625, -0.00958251953125]
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🚨 LM_HEAD: checksum=11520.000000, mean=0.000030, std=0.017456
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🚨 TOKEN 16 EMBEDDING: [-0.0081787109375, -0.0047607421875, -0.00107574462890625, 0.000896453857421875, 0.002410888671875]
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - vocab_size=152064
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🚨 TOKEN 108386 EMBEDDING: N/A
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - hidden_size=5120
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - num_layers=64
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model config - model_type=qwen2
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model training mode = False
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 Using lm_head
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model device = cuda:1
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: Model dtype = torch.bfloat16
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.temperatures = tensor([[0.7000]], device='cuda:1')
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ks = tensor([1073741824], device='cuda:1', dtype=torch.int32)
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ps = tensor([1.], device='cuda:1')
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: batch.sampling_info.is_all_greedy = False
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: Original is_all_greedy=False, temperature=0.699999988079071
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🚨 LM_HEAD: checksum=14720.000000, mean=0.000038, std=0.017700
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - vocab_size=152064
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - hidden_size=5120
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - num_layers=64
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model config - model_type=qwen2
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model training mode = False
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model device = cuda:0
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: Model dtype = torch.bfloat16
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.temperatures = tensor([[0.7000]], device='cuda:0')
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ks = tensor([1073741824], device='cuda:0', dtype=torch.int32)
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.top_ps = tensor([1.], device='cuda:0')
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: batch.sampling_info.is_all_greedy = False
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: Original is_all_greedy=False, temperature=0.699999988079071
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 VL Model: Extracted text components from tuple result
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits shape = torch.Size([1, 152064])
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits dtype = torch.float32
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits device = cuda:1
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 VL Model: Extracted text components from tuple result
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits shape = torch.Size([1, 152064])
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits dtype = torch.float32
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits device = cuda:0
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits stats: mean=0.000007, std=0.002564, min=0.000000, max=0.999989
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits stats: mean=0.000007, std=0.002564, min=0.000000, max=0.999989
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: logits anomalies: nan_count=0, inf_count=0
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: logits anomalies: nan_count=0, inf_count=0
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: top-5 logits = [0.9999891519546509, 1.0879998626478482e-05, 1.4421160932087673e-09, 1.4151962934860762e-10, 8.28244625661334e-11]
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: top-5 indices = [104198, 35946, 111308, 108386, 128296]
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: token 16 logit value = 9.311456350223675e-19
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: top-5 logits = [0.9999891519546509, 1.0879998626478482e-05, 1.4421160932087673e-09, 1.4151962934860762e-10, 8.28244625661334e-11]
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: token 108386 logit value = 1.4151962934860762e-10
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: top-5 indices = [104198, 35946, 111308, 108386, 128296]
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: token 16 logit value = 9.311456350223675e-19
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: token 108386 logit value = 1.4151962934860762e-10
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: Generated next_token_ids = tensor([104198], device='cuda:1', dtype=torch.int32)
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 DEBUG: next_token_ids type = <class 'torch.Tensor'>
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔥 Generated logits and token IDs: tensor([104198], device='cuda:1', dtype=torch.int32)
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: Generated next_token_ids = tensor([104198], device='cuda:0', dtype=torch.int32)
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 DEBUG: next_token_ids type = <class 'torch.Tensor'>
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔥 process_batch_result_prefill called with result.next_token_ids=tensor([104198], device='cuda:1', dtype=torch.int32)
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔥 result type: <class 'sglang.srt.managers.scheduler.GenerationBatchResult'>
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔥 result.logits_output: True
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔥 Generated logits and token IDs: tensor([104198], device='cuda:0', dtype=torch.int32)
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔥 Processing req b5da5f8081b24d469e1656fbdc0a88c7, next_token_id=104198
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔥 Added token 104198 to req b5da5f8081b24d469e1656fbdc0a88c7, output_ids=[104198]
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔥 Request b5da5f8081b24d469e1656fbdc0a88c7 continuing (skipping cache operations)
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔥 process_batch_result_prefill called with result.next_token_ids=tensor([104198], device='cuda:0', dtype=torch.int32)
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔥 result type: <class 'sglang.srt.managers.scheduler.GenerationBatchResult'>
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔥 result.logits_output: True
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔥 Processing req b5da5f8081b24d469e1656fbdc0a88c7, next_token_id=104198
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔥 Added token 104198 to req b5da5f8081b24d469e1656fbdc0a88c7, output_ids=[104198]
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔥 Request b5da5f8081b24d469e1656fbdc0a88c7 continuing (skipping cache operations)
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔥 next_token_logits shape: (1, 152064)
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 KV Cache Status BEFORE: avail=90, expected=0, diff=90
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 batch.out_cache_loc exists: tensor([ 0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17,
        18, 19, 20], device='cuda:1', dtype=torch.int32)
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔧 CRITICAL FIX: Freeing KV cache allocated by Prefill instance
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] ✅ CRITICAL FIX: Successfully freed KV cache in Prefill instance
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔥 Creating BatchProcessPrefillResultReq with next_token_ids=[104198]
[2025-07-21 07:09:52 PREFILL TP1] [PREFILL] 🔥 Send response to D worker
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔥 next_token_logits shape: (1, 152064)
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 KV Cache Status BEFORE: avail=90, expected=0, diff=90
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 batch.out_cache_loc exists: tensor([ 0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17,
        18, 19, 20], device='cuda:0', dtype=torch.int32)
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔧 CRITICAL FIX: Freeing KV cache allocated by Prefill instance
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] ✅ CRITICAL FIX: Successfully freed KV cache in Prefill instance
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔥 Creating BatchProcessPrefillResultReq with next_token_ids=[104198]
[2025-07-21 07:09:52 PREFILL TP0] [PREFILL] 🔥 Send response to D worker
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔥 process_prefill_result started, next_token_ids=[104198]
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔥 Got batch with 1 requests
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔥 process_prefill_result started, next_token_ids=[104198]
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔥 Got batch with 1 requests
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔥 Setting batch.output_ids...
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔥 Setting batch.output_ids...
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔧 KV Cache Status BEFORE process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔥 Calling process_batch_result_prefill...
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔧 KV Cache Status BEFORE process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔥 process_batch_result_prefill called for Semi-PD Decode instance
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔥 Calling process_batch_result_prefill...
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🚨 CRITICAL: Checking weight sharing in Decode instance...
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔥 process_batch_result_prefill called for Semi-PD Decode instance
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🚨 CRITICAL: Checking weight sharing in Decode instance...
[2025-07-21 07:09:52 DECODE TP1] [DECODE] ❌ Could not find model
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔧 DEBUG: tp_worker type = <class 'sglang.srt.managers.tp_worker_overlap_thread.TpModelWorkerClient'>
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔧 DEBUG: tp_worker attributes = ['cur_sampling_info', 'device', 'forward_batch_generation', 'forward_stream', 'forward_thread', 'forward_thread_func', 'forward_thread_func_', 'future_token_ids_ct', 'future_token_ids_limit', 'future_token_ids_map', 'get_attention_tp_cpu_group', 'get_attention_tp_group', 'get_ipc_info', 'get_kv_cache', 'get_memory_pool', 'get_pad_input_ids_func', 'get_tp_group', 'get_weights_by_name', 'get_worker_info', 'gpu_id', 'hicache_layer_transfer_counter', 'init_attention_backend', 'init_cuda_graphs', 'init_weights_update_group', 'input_queue', 'max_running_requests', 'output_queue', 'parent_process', 'register_hicache_layer_transfer_counter', 'resolve_last_batch_result', 'scheduler_stream', 'set_hicache_consumer', 'share_params_from_ipc', 'update_weights_from_disk', 'update_weights_from_distributed', 'update_weights_from_tensor', 'worker']
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔧 DEBUG: Found tp_worker.worker
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔧 DEBUG: Found tp_worker.worker.model_runner
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔧 DEBUG: Found model via tp_worker.worker.model_runner.model
[2025-07-21 07:09:52 DECODE TP0] [DECODE] ❌ Could not find model
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔧 CRITICAL FIX: Called free_group_begin() for KV Cache management
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔧 DEBUG: tp_worker type = <class 'sglang.srt.managers.tp_worker_overlap_thread.TpModelWorkerClient'>
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔧 DEBUG: tp_worker attributes = ['cur_sampling_info', 'device', 'forward_batch_generation', 'forward_stream', 'forward_thread', 'forward_thread_func', 'forward_thread_func_', 'future_token_ids_ct', 'future_token_ids_limit', 'future_token_ids_map', 'get_attention_tp_cpu_group', 'get_attention_tp_group', 'get_ipc_info', 'get_kv_cache', 'get_memory_pool', 'get_pad_input_ids_func', 'get_tp_group', 'get_weights_by_name', 'get_worker_info', 'gpu_id', 'hicache_layer_transfer_counter', 'init_attention_backend', 'init_cuda_graphs', 'init_weights_update_group', 'input_queue', 'max_running_requests', 'output_queue', 'parent_process', 'register_hicache_layer_transfer_counter', 'resolve_last_batch_result', 'scheduler_stream', 'set_hicache_consumer', 'share_params_from_ipc', 'update_weights_from_disk', 'update_weights_from_distributed', 'update_weights_from_tensor', 'worker']
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔧 DEBUG: Found tp_worker.worker
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔧 DEBUG: Found tp_worker.worker.model_runner
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔧 DEBUG: Found model via tp_worker.worker.model_runner.model
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔧 CRITICAL FIX: Called free_group_begin() for KV Cache management
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔧 DEBUG: Decode batch.out_cache_loc exists: tensor([ 1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
        19, 20, 21], device='cuda:1')
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔧 DEBUG: Decode batch.out_cache_loc exists: tensor([ 1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
        19, 20, 21], device='cuda:0')
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔧 CRITICAL FIX: Called free_group_end() to complete KV Cache management
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔥 process_batch_result_prefill completed for Semi-PD
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔧 KV Cache Status AFTER process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 07:09:52 DECODE TP1] [DECODE] ⚠️ KV Cache unchanged - potential memory leak!
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔥 process_batch_result_prefill completed!
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔥 Filtering batch...
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔥 Merging batch to running_batch...
[2025-07-21 07:09:52 DECODE TP1] [DECODE] 🔥 process_prefill_result completed successfully!
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔧 CRITICAL FIX: Called free_group_end() to complete KV Cache management
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔥 process_batch_result_prefill completed for Semi-PD
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔧 KV Cache Status AFTER process_batch_result_prefill: avail=23574, expected=23574, diff=0
[2025-07-21 07:09:52 DECODE TP0] [DECODE] ⚠️ KV Cache unchanged - potential memory leak!
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔥 process_batch_result_prefill completed!
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔥 Filtering batch...
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔥 Merging batch to running_batch...
[2025-07-21 07:09:52 DECODE TP0] [DECODE] 🔥 process_prefill_result completed successfully!
[2025-07-21 07:09:54] INFO:     127.0.0.1:47088 - "POST /v1/chat/completions HTTP/1.1" 200 OK
[2025-07-21 07:09:54 DECODE TP0] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23626, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23626
self.tree_cache.evictable_size()=0

[2025-07-21 07:09:54 DECODE TP1] Memory leak detected, attempting cache flush: token_to_kv_pool_allocator memory leak detected! available_size=23626, expected_size=23574, protected_size=0, self.max_total_num_tokens=23574
self.token_to_kv_pool_allocator.available_size()=23626
self.tree_cache.evictable_size()=0

[2025-07-21 07:09:54 DECODE TP0] Cache flushed successfully!
[2025-07-21 07:09:54 DECODE TP1] Cache flushed successfully!
[2025-07-21 07:09:54 DECODE TP0] Memory leak resolved after cache flush
[2025-07-21 07:09:54 DECODE TP1] Memory leak resolved after cache flush
start_sglang.sh: line 1: 632364 Killed                  python -m sglang.launch_server --model-path /home/<USER>/model/Qwen/Qwen2.5-32B-Instruct --tp-size 2 --disable-radix-cache --chat-template chatml --enable-semi-pd
